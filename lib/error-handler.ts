// Comprehensive error handling utilities for the Crefy Connect platform

export interface ErrorDetails {
  code: string;
  message: string;
  userMessage: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: 'network' | 'validation' | 'authentication' | 'blockchain' | 'api' | 'unknown';
  retryable: boolean;
  context?: Record<string, any>;
}

export class AppError extends Error {
  public readonly code: string;
  public readonly userMessage: string;
  public readonly severity: ErrorDetails['severity'];
  public readonly category: ErrorDetails['category'];
  public readonly retryable: boolean;
  public readonly context?: Record<string, any>;

  constructor(details: ErrorDetails) {
    super(details.message);
    this.name = 'AppError';
    this.code = details.code;
    this.userMessage = details.userMessage;
    this.severity = details.severity;
    this.category = details.category;
    this.retryable = details.retryable;
    this.context = details.context;
  }
}

// Error code constants
export const ERROR_CODES = {
  // Network errors
  NETWORK_TIMEOUT: 'NETWORK_TIMEOUT',
  NETWORK_OFFLINE: 'NETWORK_OFFLINE',
  NETWORK_ERROR: 'NETWORK_ERROR',
  
  // API errors
  API_UNAUTHORIZED: 'API_UNAUTHORIZED',
  API_FORBIDDEN: 'API_FORBIDDEN',
  API_NOT_FOUND: 'API_NOT_FOUND',
  API_CONFLICT: 'API_CONFLICT',
  API_VALIDATION: 'API_VALIDATION',
  API_SERVER_ERROR: 'API_SERVER_ERROR',
  
  // Wallet errors
  WALLET_NOT_CONNECTED: 'WALLET_NOT_CONNECTED',
  WALLET_CONNECTION_FAILED: 'WALLET_CONNECTION_FAILED',
  WALLET_NETWORK_MISMATCH: 'WALLET_NETWORK_MISMATCH',
  
  // ENS errors
  ENS_NOT_FOUND: 'ENS_NOT_FOUND',
  ENS_OWNERSHIP_FAILED: 'ENS_OWNERSHIP_FAILED',
  ENS_INVALID_FORMAT: 'ENS_INVALID_FORMAT',
  ENS_REGISTRATION_FAILED: 'ENS_REGISTRATION_FAILED',
  
  // Validation errors
  VALIDATION_REQUIRED: 'VALIDATION_REQUIRED',
  VALIDATION_FORMAT: 'VALIDATION_FORMAT',
  VALIDATION_LENGTH: 'VALIDATION_LENGTH',
  
  // Unknown errors
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
} as const;

// Error message mappings
const ERROR_MESSAGES: Record<string, Omit<ErrorDetails, 'context'>> = {
  [ERROR_CODES.NETWORK_TIMEOUT]: {
    code: ERROR_CODES.NETWORK_TIMEOUT,
    message: 'Request timed out',
    userMessage: 'The request took too long to complete. Please check your connection and try again.',
    severity: 'medium',
    category: 'network',
    retryable: true,
  },
  
  [ERROR_CODES.NETWORK_OFFLINE]: {
    code: ERROR_CODES.NETWORK_OFFLINE,
    message: 'Network is offline',
    userMessage: 'You appear to be offline. Please check your internet connection.',
    severity: 'high',
    category: 'network',
    retryable: true,
  },
  
  [ERROR_CODES.API_UNAUTHORIZED]: {
    code: ERROR_CODES.API_UNAUTHORIZED,
    message: 'Unauthorized access',
    userMessage: 'Your session has expired. Please log in again.',
    severity: 'medium',
    category: 'authentication',
    retryable: false,
  },
  
  [ERROR_CODES.API_FORBIDDEN]: {
    code: ERROR_CODES.API_FORBIDDEN,
    message: 'Access forbidden',
    userMessage: 'You do not have permission to perform this action.',
    severity: 'medium',
    category: 'authentication',
    retryable: false,
  },
  
  [ERROR_CODES.API_CONFLICT]: {
    code: ERROR_CODES.API_CONFLICT,
    message: 'Resource conflict',
    userMessage: 'This resource already exists or conflicts with existing data.',
    severity: 'medium',
    category: 'api',
    retryable: false,
  },
  
  [ERROR_CODES.WALLET_NOT_CONNECTED]: {
    code: ERROR_CODES.WALLET_NOT_CONNECTED,
    message: 'Wallet not connected',
    userMessage: 'Please connect your wallet to continue.',
    severity: 'medium',
    category: 'blockchain',
    retryable: true,
  },
  
  [ERROR_CODES.ENS_NOT_FOUND]: {
    code: ERROR_CODES.ENS_NOT_FOUND,
    message: 'ENS name not found',
    userMessage: 'This ENS name does not exist or has no owner.',
    severity: 'medium',
    category: 'blockchain',
    retryable: true,
  },
  
  [ERROR_CODES.ENS_OWNERSHIP_FAILED]: {
    code: ERROR_CODES.ENS_OWNERSHIP_FAILED,
    message: 'ENS ownership verification failed',
    userMessage: 'You do not own this ENS name. Please verify ownership or use a different name.',
    severity: 'medium',
    category: 'blockchain',
    retryable: false,
  },
};

// Error handler class
export class ErrorHandler {
  static createError(code: string, context?: Record<string, any>): AppError {
    const errorDetails = ERROR_MESSAGES[code];
    
    if (!errorDetails) {
      return new AppError({
        code: ERROR_CODES.UNKNOWN_ERROR,
        message: 'An unknown error occurred',
        userMessage: 'Something went wrong. Please try again.',
        severity: 'medium',
        category: 'unknown',
        retryable: true,
        context,
      });
    }
    
    return new AppError({
      ...errorDetails,
      context,
    });
  }
  
  static fromHttpError(status: number, message?: string, context?: Record<string, any>): AppError {
    let code: string;

    switch (status) {
      case 401:
        code = ERROR_CODES.API_UNAUTHORIZED;
        break;
      case 403:
        code = ERROR_CODES.API_FORBIDDEN;
        break;
      case 404:
        code = ERROR_CODES.API_NOT_FOUND;
        break;
      case 409:
        code = ERROR_CODES.API_CONFLICT;
        break;
      case 422:
        code = ERROR_CODES.API_VALIDATION;
        break;
      case 500:
      case 502:
      case 503:
      case 504:
        code = ERROR_CODES.API_SERVER_ERROR;
        break;
      default:
        code = ERROR_CODES.UNKNOWN_ERROR;
    }

    // Create error with custom message if provided
    const errorDetails = ERROR_MESSAGES[code];
    if (errorDetails && message) {
      return new AppError({
        ...errorDetails,
        message: message,
        context: { ...context, httpStatus: status },
      });
    }

    return this.createError(code, { ...context, httpStatus: status });
  }
  
  static fromNetworkError(error: any, context?: Record<string, any>): AppError {
    if (error.code === 'NETWORK_ERROR' || error.message?.includes('Network Error')) {
      return this.createError(ERROR_CODES.NETWORK_ERROR, context);
    }
    
    if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
      return this.createError(ERROR_CODES.NETWORK_TIMEOUT, context);
    }
    
    if (!navigator.onLine) {
      return this.createError(ERROR_CODES.NETWORK_OFFLINE, context);
    }
    
    return this.createError(ERROR_CODES.UNKNOWN_ERROR, {
      ...context,
      originalError: error.message,
    });
  }
  
  static log(error: AppError | Error): void {
    const timestamp = new Date().toISOString();
    
    if (error instanceof AppError) {
      console.error(`[${timestamp}] AppError [${error.severity}] ${error.code}: ${error.message}`, {
        category: error.category,
        retryable: error.retryable,
        context: error.context,
        stack: error.stack,
      });
    } else {
      console.error(`[${timestamp}] Error: ${error.message}`, {
        stack: error.stack,
      });
    }
    
    // Here you could integrate with external error reporting services
    // e.g., Sentry, LogRocket, etc.
  }
}

// Utility functions for common error scenarios
export const handleApiError = (error: any, context?: Record<string, any>): AppError => {
  try {
    if (error.response) {
      // HTTP error response
      return ErrorHandler.fromHttpError(
        error.response.status,
        error.response.data?.message || error.response.data?.error,
        context
      );
    } else if (error.request) {
      // Network error
      return ErrorHandler.fromNetworkError(error, context);
    } else {
      // Other error
      return ErrorHandler.createError(ERROR_CODES.UNKNOWN_ERROR, {
        ...context,
        originalError: error?.message || 'Unknown error',
        errorType: typeof error,
      });
    }
  } catch (handlerError) {
    // Fallback error if error handling itself fails
    console.error('Error in handleApiError:', handlerError);
    return new AppError({
      code: ERROR_CODES.UNKNOWN_ERROR,
      message: 'An unknown error occurred',
      userMessage: 'Something went wrong. Please try again.',
      severity: 'medium',
      category: 'unknown',
      retryable: true,
      context: {
        ...context,
        handlerError: handlerError?.message,
        originalError: error?.message,
      },
    });
  }
};

export const handleWalletError = (error: any, context?: Record<string, any>): AppError => {
  if (error.code === 4001) {
    // User rejected request
    return ErrorHandler.createError(ERROR_CODES.WALLET_CONNECTION_FAILED, {
      ...context,
      reason: 'User rejected connection',
    });
  }
  
  if (error.code === -32002) {
    // Request already pending
    return ErrorHandler.createError(ERROR_CODES.WALLET_CONNECTION_FAILED, {
      ...context,
      reason: 'Connection request already pending',
    });
  }
  
  return ErrorHandler.createError(ERROR_CODES.WALLET_CONNECTION_FAILED, {
    ...context,
    originalError: error.message,
  });
};

export const handleENSError = (error: any, context?: Record<string, any>): AppError => {
  if (error.message?.includes('ENS name not found')) {
    return ErrorHandler.createError(ERROR_CODES.ENS_NOT_FOUND, context);
  }
  
  if (error.message?.includes('ownership')) {
    return ErrorHandler.createError(ERROR_CODES.ENS_OWNERSHIP_FAILED, context);
  }
  
  return ErrorHandler.createError(ERROR_CODES.UNKNOWN_ERROR, {
    ...context,
    originalError: error.message,
  });
};
