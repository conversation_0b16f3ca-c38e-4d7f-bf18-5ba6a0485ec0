/**
 * Generate a 6-digit OTP code
 */
export function generateOTP(): string {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

/**
 * Validate OTP format
 */
export function isValidOTP(otp: string): boolean {
  return /^\d{6}$/.test(otp);
}

/**
 * Generate OTP with expiry time
 */
export function generateOTPWithExpiry(expiryMinutes: number = 10) {
  return {
    otp: generateOTP(),
    expiry: new Date(Date.now() + expiryMinutes * 60 * 1000),
  };
}

/**
 * Check if OTP has expired
 */
export function isOTPExpired(expiry: Date): boolean {
  return new Date() > expiry;
}