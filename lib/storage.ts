// In-memory storage for development (replace with database in production)
// This module provides shared storage across all API routes

export interface User {
  id?: string;
  name?: string;
  email: string;
  password?: string;
  isVerified: boolean;
  isActive: boolean;
  walletAddress?: string;
  publicKey?: string;
  socialType?: 'email' | 'google';
  googleData?: {
    id: string;
    name: string;
    picture: string;
  };
  createdAt: string;
  updatedAt?: string;
}

export interface OTPData {
  otp: string;
  expiry: Date;
}

// Shared storage instances
export const users = new Map<string, User>();
export const otpStore = new Map<string, OTPData>();

// Helper functions for user management
export function getUserByEmail(email: string): User | undefined {
  return users.get(email);
}

export function setUser(email: string, userData: User): void {
  users.set(email, userData);
}

export function deleteUser(email: string): boolean {
  return users.delete(email);
}

// Additional helper functions for social wallet authentication
export function getUser(email: string): User | undefined {
  return users.get(email);
}

export function createUser(userData: Omit<User, 'id' | 'updatedAt'>): User {
  const user: User = {
    id: generateUserId(),
    isVerified: true, // Default to verified for social auth
    ...userData,
    updatedAt: new Date().toISOString(),
  };

  users.set(user.email, user);
  return user;
}

export function updateUser(email: string, updates: Partial<User>): User {
  const existingUser = users.get(email);
  if (!existingUser) {
    throw new Error('User not found');
  }

  const updatedUser: User = {
    ...existingUser,
    ...updates,
    updatedAt: new Date().toISOString(),
  };

  users.set(email, updatedUser);
  return updatedUser;
}

function generateUserId(): string {
  return 'user_' + Math.random().toString(36).substring(2) + Date.now().toString(36);
}

// Helper functions for OTP management
export function getOTP(email: string): OTPData | undefined {
  return otpStore.get(email);
}

export function setOTP(email: string, otpData: OTPData): void {
  otpStore.set(email, otpData);
}

export function deleteOTP(email: string): boolean {
  return otpStore.delete(email);
}

// Cleanup expired OTPs (run periodically)
export function cleanupExpiredOTPs(): void {
  const now = new Date();
  for (const [email, otpData] of otpStore.entries()) {
    if (now > otpData.expiry) {
      otpStore.delete(email);
    }
  }
}

// Initialize cleanup interval (runs every 5 minutes)
if (typeof window === 'undefined') { // Only run on server
  setInterval(cleanupExpiredOTPs, 5 * 60 * 1000);
}