// Name Wrapper Contract ABI and Configuration for ENS v2 Wrapper
// Migrated from crefy-connect-backend/src/ens/ContractABIs/NameWrapper.ts

export const NAMEWRAPPER_CONTRACT_ADDRESS = "0x0635513f179D50A207757E05759CbD106d7dFcE8";

export const NAMEWRAPPER_CONTRACT_ABI = [
    {
        "inputs": [
          {
            "internalType": "address",
            "name": "from",
            "type": "address"
          },
          {
            "internalType": "address",
            "name": "to",
            "type": "address"
          },
          {
            "internalType": "uint256",
            "name": "id",
            "type": "uint256"
          },
          {
            "internalType": "uint256",
            "name": "amount",
            "type": "uint256"
          },
          {
            "internalType": "bytes",
            "name": "data",
            "type": "bytes"
          }
        ],
        "name": "safeTransferFrom",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function"
      }
    ] as const;

// TypeScript types for better type safety
export type NameWrapperContractAddress = typeof NAMEWRAPPER_CONTRACT_ADDRESS;
export type NameWrapperContractABI = typeof NAMEWRAPPER_CONTRACT_ABI;

// Contract configuration for different networks
export const NAMEWRAPPER_CONTRACT_CONFIG = {
	sepolia: {
		address: NAMEWRAPPER_CONTRACT_ADDRESS,
		abi: NAMEWRAPPER_CONTRACT_ABI,
		chainId: 11155111
	}
} as const;

// Supported chains for name wrapper contract
export type SupportedNameWrapperChain = keyof typeof NAMEWRAPPER_CONTRACT_CONFIG;
