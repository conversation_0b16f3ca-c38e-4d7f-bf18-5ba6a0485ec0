// Factory Contract ABI and Configuration for ENS Subname Registration
// Migrated from crefy-connect-backend/src/ens/ContractABIs/FactoryContract.ts

export const FACTORY_CONTRACT_ADDRESS = "0x0dBA7bd3240c86090Cd53bE0D9DaB99b466A36D3";

export const FACTORY_CONTRACT_ABI = [
	{
		"anonymous": false,
		"inputs": [
			{
				"indexed": true,
				"internalType": "address",
				"name": "owner",
				"type": "address"
			},
			{
				"indexed": false,
				"internalType": "address",
				"name": "contractAddress",
				"type": "address"
			},
			{
				"indexed": false,
				"internalType": "bytes32",
				"name": "parentNode",
				"type": "bytes32"
			}
		],
		"name": "SubnameContractCreated",
		"type": "event"
	},
	{
		"inputs": [
			{
				"internalType": "bytes32",
				"name": "_parentNode",
				"type": "bytes32"
			}
		],
		"name": "createSubnameRegistrar",
		"outputs": [
			{
				"internalType": "address",
				"name": "",
				"type": "address"
			}
		],
		"stateMutability": "nonpayable",
		"type": "function"
	},
	{
		"inputs": [],
		"name": "getAllSubnameContracts",
		"outputs": [
			{
				"internalType": "address[]",
				"name": "",
				"type": "address[]"
			}
		],
		"stateMutability": "view",
		"type": "function"
	},
	{
		"inputs": [
			{
				"internalType": "address",
				"name": "owner",
				"type": "address"
			}
		],
		"name": "getContractsByOwner",
		"outputs": [
			{
				"internalType": "address[]",
				"name": "",
				"type": "address[]"
			}
		],
		"stateMutability": "view",
		"type": "function"
	},
	{
		"inputs": [
			{
				"internalType": "bytes32",
				"name": "parentNode",
				"type": "bytes32"
			}
		],
		"name": "parentNodeExists",
		"outputs": [
			{
				"internalType": "bool",
				"name": "exists",
				"type": "bool"
			}
		],
		"stateMutability": "view",
		"type": "function"
	},
	{
		"inputs": [
			{
				"internalType": "address",
				"name": "subnameOwner",
				"type": "address"
			},
			{
				"internalType": "uint256",
				"name": "",
				"type": "uint256"
			}
		],
		"name": "subNameContractsByOwner",
		"outputs": [
			{
				"internalType": "address",
				"name": "subnameContracts",
				"type": "address"
			}
		],
		"stateMutability": "view",
		"type": "function"
	},
	{
		"inputs": [
			{
				"internalType": "uint256",
				"name": "",
				"type": "uint256"
			}
		],
		"name": "subnameContracts",
		"outputs": [
			{
				"internalType": "contract L1Subnames",
				"name": "",
				"type": "address"
			}
		],
		"stateMutability": "view",
		"type": "function"
	}
] as const;

// TypeScript types for better type safety
export type FactoryContractAddress = typeof FACTORY_CONTRACT_ADDRESS;
export type FactoryContractABI = typeof FACTORY_CONTRACT_ABI;

// Contract configuration for different networks
export const FACTORY_CONTRACT_CONFIG = {
	sepolia: {
		address: FACTORY_CONTRACT_ADDRESS,
		abi: FACTORY_CONTRACT_ABI,
		chainId: 11155111
	}
} as const;

// Supported chains for factory contract
export type SupportedFactoryChain = keyof typeof FACTORY_CONTRACT_CONFIG;
