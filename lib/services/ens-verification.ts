// ENS Verification Service
// Real blockchain-based ENS ownership verification using viem/wagmi

import { normalize } from 'viem/ens';
import { createPublicClient, http, Address } from 'viem';
import { mainnet, sepolia } from 'viem/chains';
import { ENSOwnershipData } from '@/lib/types/ens';

// ENS Registry and Resolver addresses
const ENS_REGISTRY_ADDRESS = '0x00000000000C2E074eC69A0dFb2997BA6C7d2e1e';
const ENS_PUBLIC_RESOLVER_ADDRESS = '0x231b0Ee14048e9dCcD1d247744d114a4EB5E8E63';

// Create public clients for different networks
const mainnetClient = createPublicClient({
  chain: mainnet,
  transport: http()
});

const sepoliaClient = createPublicClient({
  chain: sepolia,
  transport: http()
});

export interface ENSVerificationResult {
  isValid: boolean;
  isOwned: boolean;
  owner?: Address;
  resolver?: Address;
  contentHash?: string;
  avatar?: string;
  error?: string;
  ensName?: string;
}

export class ENSVerificationService {
  private static getClient(chainId: number = 1) {
    switch (chainId) {
      case 1:
        return mainnetClient;
      case 11155111:
        return sepoliaClient;
      default:
        return mainnetClient;
    }
  }

  /**
   * Verify ENS name ownership with multi-network support
   * @param ensName - The ENS name to verify (e.g., "vitalik.eth")
   * @param expectedOwner - The address that should own the ENS name
   * @param preferredChainId - The preferred chain ID to check on (default: 1 for mainnet)
   * @returns Promise<ENSVerificationResult>
   */
  static async verifyOwnership(
    ensName: string,
    expectedOwner: Address,
    preferredChainId: number = 1
  ): Promise<ENSVerificationResult> {
    try {
      // Normalize the ENS name
      const normalizedName = normalize(ensName.trim().toLowerCase());

      // Validate ENS name format
      if (!this.isValidENSName(normalizedName)) {
        return {
          isValid: false,
          isOwned: false,
          error: 'Invalid ENS name format',
          ensName: normalizedName
        };
      }

      // Try multiple networks for ENS resolution
      const networksToTry = [
        { chainId: 1, name: 'Mainnet' },
        { chainId: 11155111, name: 'Sepolia' }
      ];

      // Put preferred network first
      const orderedNetworks = networksToTry.sort((a, b) =>
        a.chainId === preferredChainId ? -1 : b.chainId === preferredChainId ? 1 : 0
      );

      let lastError: string | null = null;

      for (const network of orderedNetworks) {
        try {
          const client = this.getClient(network.chainId);

          // Get the owner of the ENS name
          const owner = await client.getEnsAddress({
            name: normalizedName
          });

          if (!owner) {
            lastError = `ENS name is not registered on ${network.name}`;
            continue; // Try next network
          }

          // Check if the owner matches the expected owner
          const isOwned = owner.toLowerCase() === expectedOwner.toLowerCase();

          // Get additional ENS data if owned
          let resolver: Address | undefined;
          let avatar: string | undefined;
          let contentHash: string | undefined;

          if (isOwned) {
            try {
              // Get resolver
              resolver = await client.getEnsResolver({
                name: normalizedName
              });

              // Get avatar if resolver exists
              if (resolver) {
                try {
                  avatar = await client.getEnsAvatar({
                    name: normalizedName
                  });
                } catch (error) {
                  console.warn('Failed to get ENS avatar:', error);
                }

                // Get content hash
                try {
                  const ensText = await client.getEnsText({
                    name: normalizedName,
                    key: 'contenthash'
                  });
                  contentHash = ensText || undefined;
                } catch (error) {
                  console.warn('Failed to get ENS content hash:', error);
                }
              }
            } catch (error) {
              console.warn('Failed to get additional ENS data:', error);
            }
          }

          return {
            isValid: true,
            isOwned,
            owner,
            resolver,
            avatar,
            contentHash,
            error: isOwned ? undefined : `ENS name is owned by ${owner}, not ${expectedOwner} (verified on ${network.name})`,
            ensName: normalizedName
          };

        } catch (error) {
          console.warn(`ENS verification failed on ${network.name}:`, error);
          lastError = `Failed to verify on ${network.name}: ${error instanceof Error ? error.message : 'Unknown error'}`;
          continue; // Try next network
        }
      }

      // If we get here, all networks failed
      return {
        isValid: true,
        isOwned: false,
        error: lastError || 'ENS name not found on any supported network',
        ensName: normalizedName
      };

    } catch (error) {
      console.error('ENS verification failed:', error);
      return {
        isValid: false,
        isOwned: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        ensName: ensName
      };
    }
  }

  /**
   * Get ENS name from address (reverse resolution)
   * @param address - The address to resolve
   * @param chainId - The chain ID to check on (default: 1 for mainnet)
   * @returns Promise<string | null>
   */
  static async getENSName(address: Address, chainId: number = 1): Promise<string | null> {
    try {
      const client = this.getClient(chainId);
      
      const ensName = await client.getEnsName({
        address
      });

      return ensName;
    } catch (error) {
      console.error('ENS reverse resolution failed:', error);
      return null;
    }
  }

  /**
   * Validate ENS name format
   * @param ensName - The ENS name to validate
   * @returns boolean
   */
  static isValidENSName(ensName: string): boolean {
    if (!ensName || typeof ensName !== 'string') {
      return false;
    }

    // Basic ENS name validation
    const ensPattern = /^[a-z0-9-]+\.[a-z]{2,}$/i;
    
    // Check basic format
    if (!ensPattern.test(ensName)) {
      return false;
    }

    // Check for valid TLD (most common ENS TLDs)
    const validTLDs = ['.eth', '.xyz', '.luxe', '.kred', '.art', '.club'];
    const hasValidTLD = validTLDs.some(tld => ensName.toLowerCase().endsWith(tld));
    
    if (!hasValidTLD) {
      return false;
    }

    // Check length constraints
    const parts = ensName.split('.');
    const name = parts[0];
    
    if (name.length < 3 || name.length > 63) {
      return false;
    }

    // Check for invalid characters
    if (name.startsWith('-') || name.endsWith('-')) {
      return false;
    }

    return true;
  }

  /**
   * Get ENS ownership data for display
   * @param ensName - The ENS name to get data for
   * @param chainId - The chain ID to check on (default: 1 for mainnet)
   * @returns Promise<ENSOwnershipData>
   */
  static async getOwnershipData(ensName: string, chainId: number = 1): Promise<ENSOwnershipData> {
    try {
      const normalizedName = normalize(ensName.trim().toLowerCase());
      const client = this.getClient(chainId);

      const owner = await client.getEnsAddress({
        name: normalizedName
      });

      if (!owner) {
        return {
          owner: null,
          avatar: null,
          isOwned: false
        };
      }

      // Get avatar
      let avatar: string | null = null;
      try {
        avatar = await client.getEnsAvatar({
          name: normalizedName
        });
      } catch (error) {
        console.warn('Failed to get ENS avatar:', error);
      }

      // Get resolver
      let resolver: string | undefined;
      try {
        const resolverAddress = await client.getEnsResolver({
          name: normalizedName
        });
        resolver = resolverAddress || undefined;
      } catch (error) {
        console.warn('Failed to get ENS resolver:', error);
      }

      // Get text records
      const textRecords: Record<string, string> = {};
      const commonKeys = ['email', 'url', 'twitter', 'github', 'description'];
      
      for (const key of commonKeys) {
        try {
          const value = await client.getEnsText({
            name: normalizedName,
            key
          });
          if (value) {
            textRecords[key] = value;
          }
        } catch (error) {
          // Ignore individual text record failures
        }
      }

      return {
        owner,
        avatar,
        isOwned: true,
        resolver,
        textRecords: Object.keys(textRecords).length > 0 ? textRecords : undefined
      };

    } catch (error) {
      console.error('Failed to get ENS ownership data:', error);
      return {
        owner: null,
        avatar: null,
        isOwned: false
      };
    }
  }

  /**
   * Check if an address owns any ENS names
   * @param address - The address to check
   * @param chainId - The chain ID to check on (default: 1 for mainnet)
   * @returns Promise<boolean>
   */
  static async hasENSName(address: Address, chainId: number = 1): Promise<boolean> {
    try {
      const ensName = await this.getENSName(address, chainId);
      return ensName !== null;
    } catch (error) {
      console.error('Failed to check ENS name ownership:', error);
      return false;
    }
  }
}
