'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/lib/toast-context";
import { useAccount } from 'wagmi';
import { ENSTransferOwnership } from "./ens-transfer-ownership";
import { getENSOwner, isValidENSName } from "@/lib/ens-utils";
import { 
  ArrowRightIcon, 
  AlertCircleIcon, 
  SearchIcon,
  ShieldCheckIcon,
  InfoIcon,
  GlobeIcon
} from 'lucide-react';

export interface StandaloneENSTransferProps {
  className?: string;
}

type TransferStep = 'enter-ens' | 'verify-ownership' | 'transfer';

export function StandaloneENSTransfer({
  className = ""
}: StandaloneENSTransferProps) {
  const { address, isConnected } = useAccount();
  const { showToast } = useToast();

  const [currentStep, setCurrentStep] = useState<TransferStep>('enter-ens');
  const [ensName, setEnsName] = useState('');
  const [ensOwner, setEnsOwner] = useState<string | null>(null);
  const [isVerifying, setIsVerifying] = useState(false);
  const [verificationError, setVerificationError] = useState<string | null>(null);

  // Validate ENS name format
  const validateENSName = (name: string): boolean => {
    if (!name) return false;
    return isValidENSName(name);
  };

  // Handle ENS name verification
  const handleVerifyENS = async () => {
    if (!ensName || !validateENSName(ensName)) {
      setVerificationError('Please enter a valid ENS name (e.g., myname.eth)');
      return;
    }

    if (!isConnected || !address) {
      setVerificationError('Please connect your wallet first');
      return;
    }

    setIsVerifying(true);
    setVerificationError(null);

    try {
      console.log('Verifying ENS ownership for:', ensName);
      
      // Get the current owner of the ENS name
      const owner = await getENSOwner(ensName);
      
      if (!owner) {
        setVerificationError('ENS name not found or not registered');
        return;
      }

      setEnsOwner(owner);

      // Check if the connected wallet owns this ENS
      if (owner.toLowerCase() === address.toLowerCase()) {
        showToast({
          type: 'success',
          title: 'Ownership Verified',
          description: `You own ${ensName} and can transfer ownership`
        });
        setCurrentStep('transfer');
      } else {
        setVerificationError(`You don't own this ENS name. Current owner: ${owner.slice(0, 6)}...${owner.slice(-4)}`);
      }

    } catch (error: any) {
      console.error('ENS verification error:', error);
      setVerificationError('Failed to verify ENS ownership. Please try again.');
    } finally {
      setIsVerifying(false);
    }
  };

  // Handle transfer success
  const handleTransferSuccess = (txHash: string, newOwner: string) => {
    showToast({
      type: 'success',
      title: 'Transfer Complete',
      description: `${ensName} ownership has been transferred successfully`
    });
    
    // Reset to initial state
    setCurrentStep('enter-ens');
    setEnsName('');
    setEnsOwner(null);
    setVerificationError(null);
  };

  // Handle transfer error
  const handleTransferError = (error: string) => {
    showToast({
      type: 'error',
      title: 'Transfer Failed',
      description: error
    });
  };

  // Reset to enter ENS step
  const handleReset = () => {
    setCurrentStep('enter-ens');
    setEnsName('');
    setEnsOwner(null);
    setVerificationError(null);
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent mb-2">
          Transfer ENS Ownership
        </h2>
        <p className="text-gray-600">
          Transfer ownership of any ENS domain you own to another Ethereum address
        </p>
      </div>

      {/* Step Indicator - Responsive */}
      <div className="flex items-center justify-center space-x-2 sm:space-x-4 mb-8 overflow-x-auto">
        <div className={`flex items-center ${currentStep === 'enter-ens' ? 'text-[#4A148C]' : 'text-gray-400'}`}>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center border-2 ${
            currentStep === 'enter-ens' ? 'border-[#4A148C] bg-[#4A148C] text-white' : 'border-gray-300'
          }`}>
            1
          </div>
          <span className="ml-2 text-xs sm:text-sm font-medium whitespace-nowrap">Enter ENS</span>
        </div>

        <div className={`w-4 sm:w-8 h-0.5 ${currentStep !== 'enter-ens' ? 'bg-[#4A148C]' : 'bg-gray-300'}`}></div>

        <div className={`flex items-center ${currentStep === 'verify-ownership' ? 'text-[#4A148C]' : currentStep === 'transfer' ? 'text-green-600' : 'text-gray-400'}`}>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center border-2 ${
            currentStep === 'verify-ownership' ? 'border-[#4A148C] bg-[#4A148C] text-white' :
            currentStep === 'transfer' ? 'border-green-600 bg-green-600 text-white' : 'border-gray-300'
          }`}>
            2
          </div>
          <span className="ml-2 text-xs sm:text-sm font-medium whitespace-nowrap">Verify</span>
        </div>

        <div className={`w-4 sm:w-8 h-0.5 ${currentStep === 'transfer' ? 'bg-green-600' : 'bg-gray-300'}`}></div>

        <div className={`flex items-center ${currentStep === 'transfer' ? 'text-[#4A148C]' : 'text-gray-400'}`}>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center border-2 ${
            currentStep === 'transfer' ? 'border-[#4A148C] bg-[#4A148C] text-white' : 'border-gray-300'
          }`}>
            3
          </div>
          <span className="ml-2 text-xs sm:text-sm font-medium whitespace-nowrap">Transfer</span>
        </div>
      </div>

      {/* Step 1: Enter ENS Name */}
      {currentStep === 'enter-ens' && (
        <Card className="bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl">
          <CardHeader>
            <CardTitle className="text-lg font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent flex items-center gap-2">
              <GlobeIcon className="h-5 w-5 text-[#4A148C]" />
              Enter ENS Name
            </CardTitle>
            <p className="text-sm text-gray-600">
              Enter the ENS name you want to transfer ownership of
            </p>
          </CardHeader>
          
          <CardContent className="space-y-6">
            {/* Info Banner */}
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-start gap-3">
                <InfoIcon className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                <div className="text-sm text-blue-800">
                  <p className="font-medium mb-1">Requirements</p>
                  <ul className="list-disc list-inside space-y-1 text-blue-700">
                    <li>You must own the ENS name with your connected wallet</li>
                    <li>Your wallet must be connected to proceed</li>
                    <li>ENS name must be in the format: name.eth</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* ENS Name Input */}
            <div className="space-y-2">
              <Label htmlFor="ensName" className="text-sm font-medium text-[#4A148C]">
                ENS Name
              </Label>
              <Input
                id="ensName"
                type="text"
                placeholder="myname.eth"
                value={ensName}
                onChange={(e) => {
                  setEnsName(e.target.value);
                  setVerificationError(null);
                }}
                className="border-[#7B1FA2]/30 focus:border-[#4A148C] focus:ring-[#4A148C]/20"
                disabled={isVerifying}
              />
              {verificationError && (
                <p className="text-sm text-red-600 flex items-center gap-1">
                  <AlertCircleIcon className="h-4 w-4" />
                  {verificationError}
                </p>
              )}
            </div>

            {/* Verify Button */}
            <Button
              onClick={handleVerifyENS}
              disabled={!ensName || !validateENSName(ensName) || isVerifying || !isConnected}
              className="w-full bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white hover:shadow-lg hover:shadow-[#4A148C]/25 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isVerifying ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                  Verifying Ownership...
                </>
              ) : (
                <>
                  <SearchIcon className="w-4 h-4 mr-2" />
                  Verify ENS Ownership
                </>
              )}
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Step 3: Transfer Ownership */}
      {currentStep === 'transfer' && ensName && ensOwner && (
        <div className="space-y-4">
          <ENSTransferOwnership
            ensName={ensName}
            currentOwner={ensOwner}
            onSuccess={handleTransferSuccess}
            onError={handleTransferError}
          />
          
          {/* Reset Button */}
          <div className="text-center">
            <Button
              onClick={handleReset}
              variant="outline"
              className="border-[#7B1FA2]/30 text-[#4A148C] hover:bg-[#7B1FA2]/5"
            >
              Transfer Another ENS
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
