'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectOption } from "@/components/ui/select";
import { ConnectButton } from '@rainbow-me/rainbowkit';
import { useAccount, useChainId, useWalletClient, useDisconnect } from 'wagmi';
import { useToast } from "@/lib/toast-context";
import { useAuth } from "@/lib/auth-context";
import { apiService } from "@/lib/api";
import { executePreparedTransaction, getENSTokenId, isValidChain, isValidENSName } from "@/lib/ens-utils";
import { ethers } from 'ethers';
import {
  WalletIcon,
  ArrowUpDownIcon,
  SettingsIcon,
  ArrowRightIcon,
  AlertCircleIcon,
  CheckCircleIcon,
  ExternalLinkIcon,
  UserIcon,
  ShieldCheckIcon,
  InfoIcon,
  GlobeIcon,
  LogOutIcon,
  RefreshCwIcon,
  XIcon
} from 'lucide-react';

export interface ENSTransferFunctionsProps {
  ensName?: string;
  currentOwner?: string;
  appId?: string;
  onSuccess?: (txHash: string, transferType: string) => void;
  onError?: (error: string) => void;
  className?: string;
}

type TransferType = 'registrar' | 'namewrapper';
type TransferStatus = 'idle' | 'preparing' | 'confirming' | 'pending' | 'success' | 'error';

export function ENSTransferFunctions({
  ensName: initialEnsName,
  currentOwner: initialCurrentOwner,
  appId,
  onSuccess,
  onError,
  className = ""
}: ENSTransferFunctionsProps) {
  const { address, isConnected } = useAccount();
  const chainId = useChainId();
  const { data: walletClient } = useWalletClient();
  const { disconnect } = useDisconnect();
  const { showToast } = useToast();
  const { token } = useAuth();

  // State management
  const [selectedENSName, setSelectedENSName] = useState(initialEnsName || '');
  const [ensOwner, setEnsOwner] = useState(initialCurrentOwner || '');
  const [transferType, setTransferType] = useState<TransferType>('registrar');
  const [selectedChain, setSelectedChain] = useState<string>('sepolia');
  const [newOwnerAddress, setNewOwnerAddress] = useState('');
  const [transferStatus, setTransferStatus] = useState<TransferStatus>('idle');
  const [transferError, setTransferError] = useState<string | null>(null);
  const [txHash, setTxHash] = useState<string | null>(null);
  const [addressError, setAddressError] = useState<string | null>(null);
  const [isMounted, setIsMounted] = useState(false);
  const [lastTransactionData, setLastTransactionData] = useState<any>(null);

  // Ensure component only renders on client side
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Don't render until mounted (prevents SSR issues with RainbowKit)
  if (!isMounted) {
    return (
      <Card className={`bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl hover:shadow-xl transition-all duration-300 ${className}`}>
        <CardHeader className="pb-4">
          <CardTitle className="text-lg font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent flex items-center gap-2">
            <ArrowUpDownIcon className="h-5 w-5 text-[#4A148C]" />
            ENS Transfer Functions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#4A148C]"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Validate Ethereum address
  const validateAddress = (address: string): boolean => {
    if (!address) {
      setAddressError('Address is required');
      return false;
    }

    if (!ethers.isAddress(address)) {
      setAddressError('Invalid Ethereum address format');
      return false;
    }

    if (address.toLowerCase() === ensOwner.toLowerCase()) {
      setAddressError('New owner cannot be the same as current owner');
      return false;
    }

    setAddressError(null);
    return true;
  };

  // Handle address input change
  const handleAddressChange = (value: string) => {
    setNewOwnerAddress(value);
    if (value) {
      validateAddress(value);
    } else {
      setAddressError(null);
    }
  };

  // Handle ENS name validation
  const handleENSNameChange = (value: string) => {
    setSelectedENSName(value);
    if (value && isValidENSName(value)) {
      // In a real implementation, you would verify ownership here
      setEnsOwner(address || '');
    }
  };

  // Handle wallet disconnect
  const handleDisconnectWallet = () => {
    disconnect();
    // Reset form state
    setSelectedENSName('');
    setEnsOwner('');
    setNewOwnerAddress('');
    setTransferStatus('idle');
    setTransferError(null);
    setTxHash(null);
    setAddressError(null);

    showToast({
      type: 'info',
      title: 'Wallet Disconnected',
      description: 'Your wallet has been disconnected successfully'
    });
  };

  // Handle wallet reconnection
  const handleReconnectWallet = () => {
    disconnect();

    showToast({
      type: 'info',
      title: 'Wallet Disconnected',
      description: 'Please connect your wallet again using the Connect Wallet button'
    });
  };

  // Execute transaction using wagmi wallet client
  const executeTransaction = async (transactionData: any) => {
    if (!walletClient) {
      throw new Error('Wallet client not available. Please ensure your wallet is connected.');
    }

    console.log('Executing transaction with wallet client:', {
      walletClient: walletClient.account?.address,
      connectedAddress: address,
      transactionData
    });

    // Verify the wallet client address matches the connected address
    if (walletClient.account?.address?.toLowerCase() !== address?.toLowerCase()) {
      throw new Error('Wallet client address does not match connected address. Please reconnect your wallet.');
    }

    // Validate transaction data
    if (!transactionData.to || !ethers.isAddress(transactionData.to)) {
      throw new Error('Invalid transaction: missing or invalid "to" address');
    }

    // Check if this is a self-transaction with data (which would cause the error)
    if (transactionData.to.toLowerCase() === address?.toLowerCase() &&
        transactionData.data &&
        transactionData.data !== '0x') {

      // Log the problematic transaction data for debugging
      console.error('Backend returned invalid transaction data:', {
        to: transactionData.to,
        from: address,
        data: transactionData.data,
        issue: 'Trying to send data to EOA (same as from address)'
      });

      throw new Error(`Invalid transaction: The backend returned a transaction that sends data to your own address (${address}). This is not allowed for EOA addresses. Please contact support with this error.`);
    }

    // Prepare transaction
    const tx = {
      to: transactionData.to as `0x${string}`,
      data: transactionData.data as `0x${string}`,
      value: BigInt(transactionData.value || '0'),
      gas: BigInt(transactionData.gas),
    };

    console.log('Prepared transaction:', tx);

    // Send transaction using wallet client
    const hash = await walletClient.sendTransaction(tx);

    // Return a transaction response-like object
    return {
      hash,
      wait: async () => {
        // For now, return a simple receipt-like object
        // In a real implementation, you'd wait for the transaction to be mined
        return { status: 1, transactionHash: hash };
      }
    };
  };

  // Handle registrar creation
  const handleCreateRegistrar = async () => {
    if (!isConnected || !address || !token || !selectedENSName) {
      setTransferError('Please connect your wallet, ensure you are logged in, and enter an ENS name');
      return;
    }

    if (!isValidChain(selectedChain)) {
      setTransferError('Invalid chain selected');
      return;
    }

    setTransferStatus('preparing');
    setTransferError(null);

    try {
      // Prepare registrar transaction
      const response = await apiService.prepareRegistrarTransaction(
        {
          ensName: selectedENSName,
          chain: selectedChain
        },
        token,
        appId
      );

      if (!response.success || !response.data?.data) {
        throw new Error(response.error || 'Failed to prepare registrar transaction');
      }

      // Store transaction data for debugging
      setLastTransactionData(response.data.data);

      setTransferStatus('confirming');

      // Execute the prepared transaction using wagmi wallet client
      const tx = await executeTransaction(response.data.data);

      setTransferStatus('pending');
      setTxHash(tx.hash);

      showToast({
        type: 'info',
        title: 'Registrar Creation Initiated',
        description: 'Transaction submitted. Waiting for confirmation...'
      });

      // Wait for transaction confirmation
      const receipt = await tx.wait();

      if (receipt?.status === 1) {
        setTransferStatus('success');

        showToast({
          type: 'success',
          title: 'Registrar Created',
          description: `Subname registrar for ${selectedENSName} has been created successfully`
        });

        onSuccess?.(tx.hash, 'registrar');
      } else {
        throw new Error('Transaction failed');
      }

    } catch (error: any) {
      console.error('Registrar creation error:', error);
      setTransferStatus('error');

      let errorMessage = 'Failed to create registrar';

      if (error.code === 'ACTION_REJECTED') {
        errorMessage = 'Transaction was rejected by user';
      } else if (error.message?.includes('insufficient funds')) {
        errorMessage = 'Insufficient funds for gas fees';
      } else if (error.message?.includes('External transactions to internal accounts cannot include data')) {
        errorMessage = 'Backend API Error: The server returned invalid transaction data (trying to send data to EOA). This is a known backend issue that needs to be fixed.';
      } else if (error.message?.includes('Wallet client address does not match')) {
        errorMessage = 'Wallet mismatch detected. Please use the "Switch" or "Disconnect" buttons to reconnect your wallet.';
      } else if (error.message?.includes('cannot send data to your own address')) {
        errorMessage = 'Backend API Error: The server returned a transaction targeting your wallet address instead of a contract. This is a backend configuration issue.';
      } else if (error.message) {
        errorMessage = error.message;
      }

      setTransferError(errorMessage);
      onError?.(errorMessage);

      showToast({
        type: 'error',
        title: 'Registrar Creation Failed',
        description: errorMessage
      });
    }
  };

  // Handle NameWrapper transfer
  const handleNameWrapperTransfer = async () => {
    if (!isConnected || !address || !token || !selectedENSName) {
      setTransferError('Please connect your wallet, ensure you are logged in, and enter an ENS name');
      return;
    }

    if (!validateAddress(newOwnerAddress)) {
      return;
    }

    if (!isValidChain(selectedChain)) {
      setTransferError('Invalid chain selected');
      return;
    }

    // Check if current user is the owner
    if (address.toLowerCase() !== ensOwner.toLowerCase()) {
      setTransferError('Only the current owner can transfer ownership');
      return;
    }

    setTransferStatus('preparing');
    setTransferError(null);

    try {
      // Get token ID for the ENS name
      const tokenId = getENSTokenId(selectedENSName);

      // Prepare NameWrapper transfer transaction
      const response = await apiService.prepareNameWrapperTransfer(
        {
          chain: selectedChain,
          from: ensOwner,
          to: newOwnerAddress,
          id: tokenId,
          amount: "1",
          data: "0x"
        },
        token,
        appId
      );

      if (!response.success || !response.data?.data) {
        throw new Error(response.error || 'Failed to prepare NameWrapper transfer');
      }

      // Store transaction data for debugging
      setLastTransactionData(response.data.data);

      setTransferStatus('confirming');

      // Execute the prepared transaction using wagmi wallet client
      const tx = await executeTransaction(response.data.data);

      setTransferStatus('pending');
      setTxHash(tx.hash);

      showToast({
        type: 'info',
        title: 'NameWrapper Transfer Initiated',
        description: 'Transaction submitted. Waiting for confirmation...'
      });

      // Wait for transaction confirmation
      const receipt = await tx.wait();

      if (receipt?.status === 1) {
        setTransferStatus('success');

        showToast({
          type: 'success',
          title: 'NameWrapper Transfer Complete',
          description: `${selectedENSName} has been transferred via NameWrapper successfully`
        });

        onSuccess?.(tx.hash, 'namewrapper');
      } else {
        throw new Error('Transaction failed');
      }

    } catch (error: any) {
      console.error('NameWrapper transfer error:', error);
      setTransferStatus('error');

      let errorMessage = 'Failed to transfer via NameWrapper';

      if (error.code === 'ACTION_REJECTED') {
        errorMessage = 'Transaction was rejected by user';
      } else if (error.message?.includes('insufficient funds')) {
        errorMessage = 'Insufficient funds for gas fees';
      } else if (error.message?.includes('External transactions to internal accounts cannot include data')) {
        errorMessage = 'Backend API Error: The server returned invalid transaction data (trying to send data to EOA). This is a known backend issue that needs to be fixed.';
      } else if (error.message?.includes('Wallet client address does not match')) {
        errorMessage = 'Wallet mismatch detected. Please use the "Switch" or "Disconnect" buttons to reconnect your wallet.';
      } else if (error.message?.includes('cannot send data to your own address')) {
        errorMessage = 'Backend API Error: The server returned a transaction targeting your wallet address instead of a contract. This is a backend configuration issue.';
      } else if (error.message) {
        errorMessage = error.message;
      }

      setTransferError(errorMessage);
      onError?.(errorMessage);

      showToast({
        type: 'error',
        title: 'NameWrapper Transfer Failed',
        description: errorMessage
      });
    }
  };

  // Form validation
  const isFormValid = isConnected && token && selectedENSName;
  const isRegistrarFormValid = isFormValid && selectedChain;
  const isNameWrapperFormValid = isFormValid && newOwnerAddress && !addressError && selectedChain;
  const isTransferring = ['preparing', 'confirming', 'pending'].includes(transferStatus);

  // Get network name
  const getNetworkName = () => {
    switch (chainId) {
      case 1: return 'Mainnet';
      case 11155111: return 'Sepolia';
      case 5: return 'Goerli';
      default: return 'Unknown';
    }
  };

  return (
    <Card className={`bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl hover:shadow-xl transition-all duration-300 ${className}`}>
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent flex items-center gap-2">
          <ArrowUpDownIcon className="h-5 w-5 text-[#4A148C]" />
          ENS Transfer Functions
        </CardTitle>
        <p className="text-sm text-gray-600 mt-2">
          Advanced ENS transfer operations using backend-prepared transactions.
        </p>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Wallet Connection */}
        {!isConnected ? (
          <div className="p-6 bg-gradient-to-br from-[#4A148C]/5 via-[#7B1FA2]/5 to-[#4A148C]/10 border border-[#B497D6]/30 rounded-2xl text-center">
            <div className="space-y-4">
              <div className="w-16 h-16 mx-auto bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] rounded-full flex items-center justify-center shadow-lg">
                <WalletIcon className="h-8 w-8 text-white" />
              </div>
              <div className="space-y-2">
                <h3 className="text-lg font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent">
                  Connect Your Wallet
                </h3>
                <p className="text-sm text-gray-600 max-w-md mx-auto">
                  Connect your wallet to access ENS transfer functions. This allows you to create registrar contracts and transfer ENS names.
                </p>
              </div>
              <div className="flex justify-center">
                <ConnectButton />
              </div>
            </div>
          </div>
        ) : (
          <>
            {/* Wallet Management */}
            <div className="p-4 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center">
                    <CheckCircleIcon className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-green-800">Wallet Connected</p>
                    <code className="text-xs text-green-700 bg-green-100 px-2 py-1 rounded">
                      {address}
                    </code>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    onClick={handleReconnectWallet}
                    size="sm"
                    variant="outline"
                    className="border-green-300 text-green-700 hover:bg-green-100"
                    disabled={isTransferring}
                  >
                    <RefreshCwIcon className="h-3 w-3 mr-1" />
                    Switch
                  </Button>
                  <Button
                    onClick={handleDisconnectWallet}
                    size="sm"
                    variant="outline"
                    className="border-red-300 text-red-700 hover:bg-red-100"
                    disabled={isTransferring}
                  >
                    <LogOutIcon className="h-3 w-3 mr-1" />
                    Disconnect
                  </Button>
                </div>
              </div>
            </div>

            {/* Network Info */}
            <div className="p-3 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg">
              <div className="flex items-center gap-2 text-sm">
                <InfoIcon className="h-4 w-4 text-blue-600" />
                <span className="text-blue-800">
                  Connected to {getNetworkName()} - {chainId === 1 ? '.eth domains' : '.test domains supported'}
                </span>
              </div>
            </div>

            {/* Wallet Status Debug Info */}
            <div className="p-3 bg-gradient-to-r from-gray-50 to-slate-50 border border-gray-200 rounded-lg">
              <div className="text-xs space-y-1">
                <div className="flex items-center gap-2">
                  <span className="font-medium">Wallet Status:</span>
                  <span className={isConnected ? 'text-green-600' : 'text-red-600'}>
                    {isConnected ? 'Connected' : 'Disconnected'}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="font-medium">Address:</span>
                  <code className="text-xs bg-gray-100 px-1 rounded">{address || 'None'}</code>
                </div>
                <div className="flex items-center gap-2">
                  <span className="font-medium">Wallet Client:</span>
                  <span className={walletClient ? 'text-green-600' : 'text-red-600'}>
                    {walletClient ? 'Available' : 'Not Available'}
                  </span>
                </div>
                {walletClient && (
                  <div className="flex items-center gap-2">
                    <span className="font-medium">Client Address:</span>
                    <code className="text-xs bg-gray-100 px-1 rounded">{walletClient.account?.address || 'None'}</code>
                  </div>
                )}
                {lastTransactionData && (
                  <div className="mt-2 pt-2 border-t border-gray-200">
                    <p className="font-medium text-xs mb-1">Last Backend Transaction Data:</p>
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <span className="text-xs">To:</span>
                        <code className="text-xs bg-gray-100 px-1 rounded">{lastTransactionData.to}</code>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-xs">Data:</span>
                        <code className="text-xs bg-gray-100 px-1 rounded break-all">{lastTransactionData.data?.substring(0, 20)}...</code>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-xs">Gas:</span>
                        <code className="text-xs bg-gray-100 px-1 rounded">{lastTransactionData.gas}</code>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-xs">Chain ID:</span>
                        <code className="text-xs bg-gray-100 px-1 rounded">{lastTransactionData.chainId}</code>
                      </div>
                      {lastTransactionData.to?.toLowerCase() === address?.toLowerCase() && (
                        <div className="text-xs text-red-600 font-medium">
                          ⚠️ Issue: Transaction targets your wallet address (EOA) instead of a contract!
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* ENS Name Input */}
            <div className="space-y-2">
              <Label htmlFor="ensName" className="text-sm font-medium text-[#4A148C]">
                ENS Name
              </Label>
              <Input
                id="ensName"
                type="text"
                placeholder={chainId === 1 ? "yourname.eth" : "yourname.test"}
                value={selectedENSName}
                onChange={(e) => handleENSNameChange(e.target.value)}
                className="border-[#7B1FA2]/30 focus:border-[#4A148C] focus:ring-[#4A148C]/20"
                disabled={isTransferring}
              />
              {selectedENSName && isValidENSName(selectedENSName) && (
                <p className="text-sm text-green-600 flex items-center gap-1">
                  <CheckCircleIcon className="h-4 w-4" />
                  Valid ENS name format
                </p>
              )}
            </div>

            {/* Show transfer options only when ENS name is entered */}
            {selectedENSName && isValidENSName(selectedENSName) && (
              <>
                {/* Current Owner Info */}
                <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg">
                  <div className="flex items-center gap-3">
                    <UserIcon className="h-5 w-5 text-blue-600" />
                    <div>
                      <p className="text-sm font-medium text-blue-800">Selected ENS: {selectedENSName}</p>
                      <code className="text-xs text-blue-700 bg-blue-100 px-2 py-1 rounded">
                        Connected as: {address}
                      </code>
                    </div>
                  </div>
                </div>

                {/* Chain Selection */}
                <div className="space-y-2">
                  <Label htmlFor="chain" className="text-sm font-medium text-[#4A148C]">
                    Blockchain Network
                  </Label>
                  <Select
                    value={selectedChain}
                    onChange={(e) => setSelectedChain(e.target.value)}
                    disabled={isTransferring}
                    className="border-[#7B1FA2]/30 focus:border-[#4A148C] focus:ring-[#4A148C]/20"
                  >
                    <SelectOption value="sepolia">Sepolia Testnet</SelectOption>
                    <SelectOption value="mainnet">Ethereum Mainnet</SelectOption>
                    <SelectOption value="goerli">Goerli Testnet</SelectOption>
                  </Select>
                </div>

                {/* Transfer Type Selection */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-[#4A148C]">
                    Transfer Operation
                  </Label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <Button
                      onClick={() => setTransferType('registrar')}
                      variant={transferType === 'registrar' ? 'default' : 'outline'}
                      className={`h-auto p-4 text-left ${
                        transferType === 'registrar'
                          ? 'bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white'
                          : 'border-[#7B1FA2]/30 hover:bg-[#7B1FA2]/5'
                      }`}
                      disabled={isTransferring}
                    >
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <SettingsIcon className="h-4 w-4" />
                          <span className="font-medium">Create Registrar</span>
                        </div>
                        <p className="text-xs opacity-80">
                          Create a subname registrar contract
                        </p>
                      </div>
                    </Button>

                    <Button
                      onClick={() => setTransferType('namewrapper')}
                      variant={transferType === 'namewrapper' ? 'default' : 'outline'}
                      className={`h-auto p-4 text-left ${
                        transferType === 'namewrapper'
                          ? 'bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white'
                          : 'border-[#7B1FA2]/30 hover:bg-[#7B1FA2]/5'
                      }`}
                      disabled={isTransferring}
                    >
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <ArrowRightIcon className="h-4 w-4" />
                          <span className="font-medium">NameWrapper Transfer</span>
                        </div>
                        <p className="text-xs opacity-80">
                          Transfer wrapped ENS name
                        </p>
                      </div>
                    </Button>
                  </div>
                </div>

                {/* NameWrapper Transfer - New Owner Input */}
                {transferType === 'namewrapper' && (
                  <div className="space-y-2">
                    <Label htmlFor="newOwner" className="text-sm font-medium text-[#4A148C]">
                      New Owner Address
                    </Label>
                    <Input
                      id="newOwner"
                      type="text"
                      placeholder="0x..."
                      value={newOwnerAddress}
                      onChange={(e) => handleAddressChange(e.target.value)}
                      className={`border-[#7B1FA2]/30 focus:border-[#4A148C] focus:ring-[#4A148C]/20 ${
                        addressError ? 'border-red-500' : ''
                      }`}
                      disabled={isTransferring}
                    />
                    {addressError && (
                      <p className="text-sm text-red-600 flex items-center gap-1">
                        <AlertCircleIcon className="h-4 w-4" />
                        {addressError}
                      </p>
                    )}
                  </div>
                )}

                {/* Backend Issue Warning */}
                <div className="p-4 bg-gradient-to-r from-red-50 to-pink-50 border border-red-200 rounded-lg">
                  <div className="flex items-start gap-3">
                    <AlertCircleIcon className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
                    <div className="text-sm">
                      <p className="text-red-800 font-medium mb-1">Known Backend Issue</p>
                      <p className="text-red-700 mb-2">
                        The backend is currently returning invalid transaction data (sending data to EOA addresses).
                        This will cause transactions to fail with "External transactions to internal accounts cannot include data" error.
                      </p>
                      <p className="text-red-600 text-xs">
                        This is a backend API issue that needs to be fixed. The transaction should target a contract address, not your wallet address.
                      </p>
                    </div>
                  </div>
                </div>

                {/* Warning */}
                <div className="p-4 bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200 rounded-lg">
                  <div className="flex items-start gap-3">
                    <AlertCircleIcon className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" />
                    <div className="text-sm">
                      <p className="text-amber-800 font-medium mb-1">Important Information</p>
                      <p className="text-amber-700">
                        {transferType === 'registrar'
                          ? 'Creating a registrar contract will allow subname management for this ENS domain. This operation requires gas fees.'
                          : 'NameWrapper transfers are irreversible. Once transferred, you will lose control of this ENS domain. Make sure the new owner address is correct.'
                        }
                      </p>
                    </div>
                  </div>
                </div>

                {/* Transfer Error */}
                {transferError && (
                  <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                    <div className="flex items-start gap-3">
                      <AlertCircleIcon className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
                      <div className="text-sm">
                        <p className="text-red-800 font-medium">Operation Failed</p>
                        <p className="text-red-700">{transferError}</p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Success Message */}
                {transferStatus === 'success' && txHash && (
                  <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                    <div className="flex items-start gap-3">
                      <CheckCircleIcon className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                      <div className="text-sm flex-1">
                        <p className="text-green-800 font-medium mb-1">Operation Successful!</p>
                        <p className="text-green-700 mb-2">
                          {transferType === 'registrar'
                            ? `Registrar contract for ${selectedENSName} has been created successfully.`
                            : `${selectedENSName} has been transferred via NameWrapper successfully.`
                          }
                        </p>
                        <Button
                          onClick={() => window.open(`https://etherscan.io/tx/${txHash}`, '_blank')}
                          size="sm"
                          variant="outline"
                          className="border-green-300 hover:bg-green-100 text-green-700"
                        >
                          <ExternalLinkIcon className="mr-1 h-3 w-3" />
                          View Transaction
                        </Button>
                      </div>
                    </div>
                  </div>
                )}

                {/* Action Button */}
                <Button
                  onClick={transferType === 'registrar' ? handleCreateRegistrar : handleNameWrapperTransfer}
                  disabled={
                    transferType === 'registrar'
                      ? !isRegistrarFormValid || isTransferring || transferStatus === 'success'
                      : !isNameWrapperFormValid || isTransferring || transferStatus === 'success'
                  }
                  className={`w-full transition-all duration-300 ${
                    (transferType === 'registrar' ? !isRegistrarFormValid : !isNameWrapperFormValid) ||
                    isTransferring ||
                    transferStatus === 'success'
                      ? 'opacity-50 cursor-not-allowed'
                      : 'bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] hover:from-[#5A1A9C] hover:to-[#8B2FAC] text-white shadow-lg hover:shadow-xl transform hover:scale-105'
                  }`}
                >
                  {transferStatus === 'preparing' ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                      Preparing Transaction...
                    </>
                  ) : transferStatus === 'confirming' ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                      Confirm in Wallet...
                    </>
                  ) : transferStatus === 'pending' ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                      Transaction Pending...
                    </>
                  ) : transferStatus === 'success' ? (
                    <>
                      <CheckCircleIcon className="w-4 h-4 mr-2" />
                      Operation Complete
                    </>
                  ) : (
                    <>
                      {transferType === 'registrar' ? (
                        <>
                          <SettingsIcon className="w-4 h-4 mr-2" />
                          Create Registrar Contract
                        </>
                      ) : (
                        <>
                          <ShieldCheckIcon className="w-4 h-4 mr-2" />
                          Transfer via NameWrapper
                        </>
                      )}
                    </>
                  )}
                </Button>

                {/* Transaction Hash */}
                {txHash && transferStatus === 'pending' && (
                  <div className="text-center">
                    <p className="text-sm text-gray-600 mb-2">Transaction Hash:</p>
                    <code className="text-xs bg-gray-100 px-2 py-1 rounded break-all">
                      {txHash}
                    </code>
                  </div>
                )}
              </>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}
