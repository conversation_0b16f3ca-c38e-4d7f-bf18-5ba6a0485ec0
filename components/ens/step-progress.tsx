'use client';

import React from 'react';
import { Check<PERSON><PERSON>cleIcon, CircleIcon, LockIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface Step {
  id: string;
  title: string;
  description: string;
  status: 'completed' | 'current' | 'upcoming' | 'locked';
  icon?: React.ReactNode;
}

interface StepProgressProps {
  steps: Step[];
  currentStepId: string;
  onStepClick?: (stepId: string) => void;
  className?: string;
}

export function StepProgress({ steps, currentStepId, onStepClick, className }: StepProgressProps) {
  const currentStepIndex = steps.findIndex(step => step.id === currentStepId);

  return (
    <div className={cn("w-full", className)}>
      {/* Mobile Progress Bar */}
      <div className="block lg:hidden mb-6">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-[#4A148C]">
            Step {currentStepIndex + 1} of {steps.length}
          </span>
          <span className="text-xs text-gray-600">
            {Math.round(((currentStepIndex + 1) / steps.length) * 100)}% Complete
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] h-2 rounded-full transition-all duration-300"
            style={{ width: `${((currentStepIndex + 1) / steps.length) * 100}%` }}
          />
        </div>
        <div className="mt-3 p-4 bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 rounded-lg">
          <h3 className="font-semibold text-[#4A148C] mb-1">
            {steps[currentStepIndex]?.title}
          </h3>
          <p className="text-sm text-gray-600">
            {steps[currentStepIndex]?.description}
          </p>
        </div>
      </div>

      {/* Desktop Step List */}
      <div className="hidden lg:block">
        <nav aria-label="Progress">
          <ol className="space-y-4">
            {steps.map((step, stepIndex) => {
              const isClickable = onStepClick && step.status !== 'locked' && step.status !== 'upcoming';
              
              return (
                <li key={step.id}>
                  <div
                    className={cn(
                      "group flex items-start p-4 rounded-xl border transition-all duration-200",
                      step.status === 'current' && "bg-gradient-to-r from-[#4A148C]/5 to-[#7B1FA2]/5 border-[#B497D6]/40 shadow-md",
                      step.status === 'completed' && "bg-green-50 border-green-200 hover:bg-green-100",
                      step.status === 'upcoming' && "bg-gray-50 border-gray-200",
                      step.status === 'locked' && "bg-gray-50 border-gray-200 opacity-60",
                      isClickable && "cursor-pointer hover:shadow-lg hover:scale-[1.02]"
                    )}
                    onClick={() => isClickable && onStepClick(step.id)}
                  >
                    {/* Step Icon */}
                    <div className="flex-shrink-0 mr-4">
                      <div
                        className={cn(
                          "w-10 h-10 rounded-full flex items-center justify-center transition-all duration-200",
                          step.status === 'completed' && "bg-green-100 text-green-600",
                          step.status === 'current' && "bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white shadow-lg",
                          step.status === 'upcoming' && "bg-gray-100 text-gray-400",
                          step.status === 'locked' && "bg-gray-100 text-gray-300"
                        )}
                      >
                        {step.status === 'completed' && <CheckCircleIcon className="h-5 w-5" />}
                        {step.status === 'current' && (step.icon || <CircleIcon className="h-5 w-5 fill-current" />)}
                        {step.status === 'upcoming' && <CircleIcon className="h-5 w-5" />}
                        {step.status === 'locked' && <LockIcon className="h-5 w-5" />}
                      </div>
                    </div>

                    {/* Step Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <h3
                          className={cn(
                            "text-sm font-semibold transition-colors",
                            step.status === 'completed' && "text-green-800",
                            step.status === 'current' && "text-[#4A148C]",
                            step.status === 'upcoming' && "text-gray-500",
                            step.status === 'locked' && "text-gray-400"
                          )}
                        >
                          {step.title}
                        </h3>
                        {step.status === 'current' && (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white">
                            Current
                          </span>
                        )}
                      </div>
                      <p
                        className={cn(
                          "mt-1 text-xs leading-relaxed",
                          step.status === 'completed' && "text-green-700",
                          step.status === 'current' && "text-[#4A148C]/80",
                          step.status === 'upcoming' && "text-gray-500",
                          step.status === 'locked' && "text-gray-400"
                        )}
                      >
                        {step.description}
                      </p>
                    </div>

                    {/* Connection Line */}
                    {stepIndex < steps.length - 1 && (
                      <div className="absolute left-9 mt-12 w-0.5 h-4 bg-gray-200" />
                    )}
                  </div>
                </li>
              );
            })}
          </ol>
        </nav>
      </div>
    </div>
  );
}

export default StepProgress;
