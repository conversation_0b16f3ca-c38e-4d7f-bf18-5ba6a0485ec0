'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectOption } from "@/components/ui/select";
import { useToast } from "@/lib/toast-context";
import { useAccount, useChainId } from 'wagmi';
import { getENSOwner, isValidENSName, isTestnetENS } from "@/lib/ens-utils";
import { 
  SearchIcon, 
  AlertCircleIcon, 
  CheckCircleIcon,
  GlobeIcon,
  RefreshCwIcon,
  InfoIcon
} from 'lucide-react';

export interface ENSNameSelectorProps {
  selectedENSName: string;
  onENSNameSelect: (ensName: string, owner: string) => void;
  onError?: (error: string) => void;
  className?: string;
}

type ValidationStatus = 'idle' | 'checking' | 'valid' | 'invalid' | 'not-owned';

export function ENSNameSelector({
  selectedENSName,
  onENSNameSelect,
  onError,
  className = ""
}: ENSNameSelectorProps) {
  const { address, isConnected } = useAccount();
  const chainId = useChainId();
  const { showToast } = useToast();

  const [ensName, setEnsName] = useState(selectedENSName);
  const [validationStatus, setValidationStatus] = useState<ValidationStatus>('idle');
  const [ensOwner, setEnsOwner] = useState<string | null>(null);
  const [validationError, setValidationError] = useState<string | null>(null);
  const [ownedENSNames, setOwnedENSNames] = useState<string[]>([]);
  const [loadingOwnedNames, setLoadingOwnedNames] = useState(false);

  // Validate ENS name and check ownership
  const validateENSName = async (name: string) => {
    if (!name) {
      setValidationStatus('idle');
      setEnsOwner(null);
      setValidationError(null);
      return;
    }

    if (!isValidENSName(name)) {
      setValidationStatus('invalid');
      setValidationError('Invalid ENS name format. Use format: name.eth or name.test');
      setEnsOwner(null);
      return;
    }

    setValidationStatus('checking');
    setValidationError(null);

    try {
      const owner = await getENSOwner(name, undefined, chainId);
      
      if (!owner) {
        setValidationStatus('not-owned');
        setValidationError('ENS name not found or not registered');
        setEnsOwner(null);
        return;
      }

      setEnsOwner(owner);
      
      if (isConnected && address && owner.toLowerCase() === address.toLowerCase()) {
        setValidationStatus('valid');
        onENSNameSelect(name, owner);
      } else {
        setValidationStatus('invalid');
        setValidationError('You are not the owner of this ENS name');
      }
    } catch (error) {
      console.error('ENS validation error:', error);
      setValidationStatus('invalid');
      setValidationError('Failed to validate ENS name');
      setEnsOwner(null);
      onError?.('Failed to validate ENS name');
    }
  };

  // Load ENS names owned by connected wallet (mock implementation)
  const loadOwnedENSNames = async () => {
    if (!isConnected || !address) return;

    setLoadingOwnedNames(true);
    try {
      // TODO: Implement actual ENS name lookup for owned domains
      // This would require querying ENS events or using a service like The Graph
      
      // Mock data for demonstration
      const mockOwnedNames = [
        'myproject.eth',
        'defiapp.eth',
        'testdomain.test'
      ];
      
      // Filter based on current chain (mainnet vs testnet)
      const filteredNames = mockOwnedNames.filter(name => {
        const isTestnet = isTestnetENS(name);
        const isMainnet = chainId === 1;
        const isSepoliaOrGoerli = chainId === 11155111 || chainId === 5;
        
        return (isTestnet && isSepoliaOrGoerli) || (!isTestnet && isMainnet);
      });
      
      setOwnedENSNames(filteredNames);
    } catch (error) {
      console.error('Failed to load owned ENS names:', error);
      setOwnedENSNames([]);
    } finally {
      setLoadingOwnedNames(false);
    }
  };

  // Handle ENS name input change
  const handleENSNameChange = (value: string) => {
    setEnsName(value);
    // Debounce validation
    const timeoutId = setTimeout(() => {
      validateENSName(value);
    }, 500);

    return () => clearTimeout(timeoutId);
  };

  // Handle selecting from owned ENS names
  const handleSelectOwnedENS = (name: string) => {
    setEnsName(name);
    validateENSName(name);
  };

  // Load owned ENS names when wallet connects
  useEffect(() => {
    if (isConnected && address) {
      loadOwnedENSNames();
    } else {
      setOwnedENSNames([]);
    }
  }, [isConnected, address, chainId]);

  // Validate initial ENS name
  useEffect(() => {
    if (selectedENSName) {
      validateENSName(selectedENSName);
    }
  }, [selectedENSName, chainId]);

  const getNetworkName = () => {
    switch (chainId) {
      case 1: return 'Mainnet';
      case 11155111: return 'Sepolia';
      case 5: return 'Goerli';
      default: return 'Unknown';
    }
  };

  return (
    <Card className={`bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl ${className}`}>
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent flex items-center gap-2">
          <GlobeIcon className="h-5 w-5 text-[#4A148C]" />
          Select ENS Name
        </CardTitle>
        <p className="text-sm text-gray-600 mt-2">
          Choose an ENS name you own to perform transfer operations on {getNetworkName()}.
        </p>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Network Info */}
        <div className="p-3 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg">
          <div className="flex items-center gap-2 text-sm">
            <InfoIcon className="h-4 w-4 text-blue-600" />
            <span className="text-blue-800">
              Connected to {getNetworkName()} - {chainId === 1 ? '.eth domains' : '.test domains supported'}
            </span>
          </div>
        </div>

        {/* Owned ENS Names */}
        {ownedENSNames.length > 0 && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label className="text-sm font-medium text-[#4A148C]">Your ENS Names</Label>
              <Button
                onClick={loadOwnedENSNames}
                variant="outline"
                size="sm"
                disabled={loadingOwnedNames}
                className="border-[#7B1FA2]/30 text-[#4A148C] hover:bg-[#7B1FA2]/5"
              >
                <RefreshCwIcon className={`h-3 w-3 ${loadingOwnedNames ? 'animate-spin' : ''}`} />
              </Button>
            </div>
            <Select
              value={ensName}
              onChange={(e) => handleSelectOwnedENS(e.target.value)}
              className="border-[#7B1FA2]/30 focus:border-[#4A148C] focus:ring-[#4A148C]/20"
            >
              <SelectOption value="">Select an ENS name...</SelectOption>
              {ownedENSNames.map((name) => (
                <SelectOption key={name} value={name}>
                  {name}
                </SelectOption>
              ))}
            </Select>
          </div>
        )}

        {/* Manual ENS Input */}
        <div className="space-y-2">
          <Label htmlFor="ensName" className="text-sm font-medium text-[#4A148C]">
            Or Enter ENS Name Manually
          </Label>
          <div className="relative">
            <Input
              id="ensName"
              type="text"
              placeholder={chainId === 1 ? "yourname.eth" : "yourname.test"}
              value={ensName}
              onChange={(e) => handleENSNameChange(e.target.value)}
              className={`border-[#7B1FA2]/30 focus:border-[#4A148C] focus:ring-[#4A148C]/20 pr-10 ${
                validationStatus === 'valid' ? 'border-green-500' : 
                validationStatus === 'invalid' || validationStatus === 'not-owned' ? 'border-red-500' : ''
              }`}
            />
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              {validationStatus === 'checking' && (
                <div className="w-4 h-4 border-2 border-[#4A148C] border-t-transparent rounded-full animate-spin"></div>
              )}
              {validationStatus === 'valid' && (
                <CheckCircleIcon className="h-4 w-4 text-green-600" />
              )}
              {(validationStatus === 'invalid' || validationStatus === 'not-owned') && (
                <AlertCircleIcon className="h-4 w-4 text-red-600" />
              )}
            </div>
          </div>
          
          {validationError && (
            <p className="text-sm text-red-600 flex items-center gap-1">
              <AlertCircleIcon className="h-4 w-4" />
              {validationError}
            </p>
          )}
          
          {validationStatus === 'valid' && ensOwner && (
            <p className="text-sm text-green-600 flex items-center gap-1">
              <CheckCircleIcon className="h-4 w-4" />
              Verified ownership of {ensName}
            </p>
          )}
        </div>

        {/* Help Text */}
        <div className="p-3 bg-gray-50 border border-gray-200 rounded-lg">
          <p className="text-xs text-gray-600">
            <strong>Note:</strong> You can only perform transfer operations on ENS names you own. 
            {chainId === 1 ? ' Use .eth domains on Mainnet.' : ' Use .test domains on testnets.'}
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
