'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { ChevronLeftIcon, ChevronRightIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

interface BreadcrumbItem {
  id: string;
  label: string;
  href?: string;
  isActive?: boolean;
}

interface BreadcrumbNavigationProps {
  items: BreadcrumbItem[];
  onPrevious?: () => void;
  onNext?: () => void;
  previousLabel?: string;
  nextLabel?: string;
  canGoPrevious?: boolean;
  canGoNext?: boolean;
  isLoading?: boolean;
  className?: string;
}

export function BreadcrumbNavigation({
  items,
  onPrevious,
  onNext,
  previousLabel = "Previous",
  nextLabel = "Next",
  canGoPrevious = true,
  canGoNext = true,
  isLoading = false,
  className
}: BreadcrumbNavigationProps) {
  return (
    <div className={cn("flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 p-4 bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 rounded-xl shadow-sm", className)}>
      {/* Breadcrumb Trail */}
      <nav className="flex items-center space-x-1 text-sm" aria-label="Breadcrumb">
        <ol className="flex items-center space-x-1">
          {items.map((item, index) => (
            <li key={item.id} className="flex items-center">
              {index > 0 && (
                <ChevronRightIcon className="h-4 w-4 text-gray-400 mx-1" />
              )}
              <span
                className={cn(
                  "px-2 py-1 rounded-md transition-colors",
                  item.isActive
                    ? "bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white font-medium"
                    : "text-gray-600 hover:text-[#4A148C]"
                )}
              >
                {item.label}
              </span>
            </li>
          ))}
        </ol>
      </nav>

      {/* Navigation Buttons */}
      <div className="flex items-center gap-2">
        {onPrevious && (
          <Button
            variant="outline"
            size="sm"
            onClick={onPrevious}
            disabled={!canGoPrevious || isLoading}
            className="border-[#B497D6]/30 hover:border-[#4A148C] hover:bg-gradient-to-r hover:from-[#4A148C]/10 hover:to-[#7B1FA2]/10 hover:text-[#4A148C] transition-all duration-300"
          >
            <ChevronLeftIcon className="h-4 w-4 mr-1" />
            {previousLabel}
          </Button>
        )}
        
        {onNext && (
          <Button
            size="sm"
            onClick={onNext}
            disabled={!canGoNext || isLoading}
            className="bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white hover:shadow-lg hover:shadow-[#4A148C]/25 transition-all duration-300"
          >
            {isLoading ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                Processing...
              </>
            ) : (
              <>
                {nextLabel}
                <ChevronRightIcon className="h-4 w-4 ml-1" />
              </>
            )}
          </Button>
        )}
      </div>
    </div>
  );
}

export default BreadcrumbNavigation;
