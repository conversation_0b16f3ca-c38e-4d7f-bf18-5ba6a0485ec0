'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectOption } from "@/components/ui/select";
import { useToast } from "@/lib/toast-context";
import { useAccount, useChainId } from 'wagmi';
import { ConnectButton } from '@rainbow-me/rainbowkit';
import { apiService } from "@/lib/api";
import { useAuth } from "@/lib/auth-context";
import { executePreparedTransaction, getENSTokenId, isValid<PERSON>hain, getChainId } from "@/lib/ens-utils";
import { ENSNameSelector } from "@/components/ens/ens-name-selector";
import { ethers } from 'ethers';
import {
  ArrowRightIcon,
  AlertCircleIcon,
  CheckCircleIcon,
  ExternalLinkIcon,
  UserIcon,
  ShieldCheckIcon,
  SettingsIcon,
  ArrowUpDownIcon,
  WalletIcon,
  InfoIcon
} from 'lucide-react';

export interface ENSTransferFunctionsProps {
  ensName?: string;
  currentOwner?: string;
  appId?: string;
  onSuccess?: (txHash: string, transferType: string) => void;
  onError?: (error: string) => void;
  className?: string;
}

type TransferType = 'registrar' | 'namewrapper';
type TransferStatus = 'idle' | 'preparing' | 'confirming' | 'pending' | 'success' | 'error';

export function ENSTransferFunctions({
  ensName: initialEnsName,
  currentOwner: initialCurrentOwner,
  appId,
  onSuccess,
  onError,
  className = ""
}: ENSTransferFunctionsProps) {
  const { address, isConnected } = useAccount();
  const chainId = useChainId();
  const { showToast } = useToast();
  const { token } = useAuth();

  const [selectedENSName, setSelectedENSName] = useState(initialEnsName || '');
  const [ensOwner, setEnsOwner] = useState(initialCurrentOwner || '');
  const [transferType, setTransferType] = useState<TransferType>('registrar');
  const [selectedChain, setSelectedChain] = useState<string>('sepolia');
  const [newOwnerAddress, setNewOwnerAddress] = useState('');
  const [transferStatus, setTransferStatus] = useState<TransferStatus>('idle');
  const [transferError, setTransferError] = useState<string | null>(null);
  const [txHash, setTxHash] = useState<string | null>(null);
  const [addressError, setAddressError] = useState<string | null>(null);

  // Handle ENS name selection
  const handleENSNameSelect = (ensName: string, owner: string) => {
    setSelectedENSName(ensName);
    setEnsOwner(owner);
    setTransferError(null);

    showToast({
      type: 'success',
      title: 'ENS Name Selected',
      description: `Selected ${ensName} for transfer operations`
    });
  };

  // Validate Ethereum address
  const validateAddress = (address: string): boolean => {
    if (!address) {
      setAddressError('Address is required');
      return false;
    }

    if (!ethers.isAddress(address)) {
      setAddressError('Invalid Ethereum address format');
      return false;
    }

    if (address.toLowerCase() === currentOwner.toLowerCase()) {
      setAddressError('New owner cannot be the same as current owner');
      return false;
    }

    setAddressError(null);
    return true;
  };

  // Handle address input change
  const handleAddressChange = (value: string) => {
    setNewOwnerAddress(value);
    if (value) {
      validateAddress(value);
    } else {
      setAddressError(null);
    }
  };

  // Handle registrar creation
  const handleCreateRegistrar = async () => {
    if (!isConnected || !address || !token) {
      setTransferError('Please connect your wallet and ensure you are logged in');
      return;
    }

    if (!isValidChain(selectedChain)) {
      setTransferError('Invalid chain selected');
      return;
    }

    setTransferStatus('preparing');
    setTransferError(null);

    try {
      // Prepare registrar transaction
      const response = await apiService.prepareRegistrarTransaction(
        {
          ensName: selectedENSName,
          chain: selectedChain
        },
        token,
        appId
      );

      if (!response.success || !response.data?.data) {
        throw new Error(response.error || 'Failed to prepare registrar transaction');
      }

      setTransferStatus('confirming');

      // Execute the prepared transaction
      const tx = await executePreparedTransaction(response.data.data);
      
      setTransferStatus('pending');
      setTxHash(tx.hash);

      showToast({
        type: 'info',
        title: 'Registrar Creation Initiated',
        description: 'Transaction submitted. Waiting for confirmation...'
      });

      // Wait for transaction confirmation
      const receipt = await tx.wait();
      
      if (receipt?.status === 1) {
        setTransferStatus('success');
        
        showToast({
          type: 'success',
          title: 'Registrar Created',
          description: `Subname registrar for ${ensName} has been created successfully`
        });

        onSuccess?.(tx.hash, 'registrar');
      } else {
        throw new Error('Transaction failed');
      }

    } catch (error: any) {
      console.error('Registrar creation error:', error);
      setTransferStatus('error');
      
      let errorMessage = 'Failed to create registrar';
      
      if (error.code === 'ACTION_REJECTED') {
        errorMessage = 'Transaction was rejected by user';
      } else if (error.message?.includes('insufficient funds')) {
        errorMessage = 'Insufficient funds for gas fees';
      } else if (error.message) {
        errorMessage = error.message;
      }

      setTransferError(errorMessage);
      onError?.(errorMessage);

      showToast({
        type: 'error',
        title: 'Registrar Creation Failed',
        description: errorMessage
      });
    }
  };

  // Handle NameWrapper transfer
  const handleNameWrapperTransfer = async () => {
    if (!isConnected || !address || !token) {
      setTransferError('Please connect your wallet and ensure you are logged in');
      return;
    }

    if (!validateAddress(newOwnerAddress)) {
      return;
    }

    if (!isValidChain(selectedChain)) {
      setTransferError('Invalid chain selected');
      return;
    }

    // Check if current user is the owner
    if (address.toLowerCase() !== ensOwner.toLowerCase()) {
      setTransferError('Only the current owner can transfer ownership');
      return;
    }

    setTransferStatus('preparing');
    setTransferError(null);

    try {
      // Get token ID for the ENS name
      const tokenId = getENSTokenId(selectedENSName);

      // Prepare NameWrapper transfer transaction
      const response = await apiService.prepareNameWrapperTransfer(
        {
          chain: selectedChain,
          from: ensOwner,
          to: newOwnerAddress,
          id: tokenId,
          amount: "1",
          data: "0x"
        },
        token,
        appId
      );

      if (!response.success || !response.data?.data) {
        throw new Error(response.error || 'Failed to prepare NameWrapper transfer');
      }

      setTransferStatus('confirming');

      // Execute the prepared transaction
      const tx = await executePreparedTransaction(response.data.data);
      
      setTransferStatus('pending');
      setTxHash(tx.hash);

      showToast({
        type: 'info',
        title: 'NameWrapper Transfer Initiated',
        description: 'Transaction submitted. Waiting for confirmation...'
      });

      // Wait for transaction confirmation
      const receipt = await tx.wait();
      
      if (receipt?.status === 1) {
        setTransferStatus('success');
        
        showToast({
          type: 'success',
          title: 'NameWrapper Transfer Complete',
          description: `${ensName} has been transferred via NameWrapper successfully`
        });

        onSuccess?.(tx.hash, 'namewrapper');
      } else {
        throw new Error('Transaction failed');
      }

    } catch (error: any) {
      console.error('NameWrapper transfer error:', error);
      setTransferStatus('error');
      
      let errorMessage = 'Failed to transfer via NameWrapper';
      
      if (error.code === 'ACTION_REJECTED') {
        errorMessage = 'Transaction was rejected by user';
      } else if (error.message?.includes('insufficient funds')) {
        errorMessage = 'Insufficient funds for gas fees';
      } else if (error.message) {
        errorMessage = error.message;
      }

      setTransferError(errorMessage);
      onError?.(errorMessage);

      showToast({
        type: 'error',
        title: 'NameWrapper Transfer Failed',
        description: errorMessage
      });
    }
  };

  const isFormValid = isConnected && token && selectedENSName && ensOwner;
  const isRegistrarFormValid = isFormValid && selectedChain;
  const isNameWrapperFormValid = isFormValid && newOwnerAddress && !addressError && selectedChain;
  const isTransferring = ['preparing', 'confirming', 'pending'].includes(transferStatus);

  return (
    <Card className={`bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl hover:shadow-xl transition-all duration-300 ${className}`}>
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent flex items-center gap-2">
          <ArrowUpDownIcon className="h-5 w-5 text-[#4A148C]" />
          ENS Transfer Functions
        </CardTitle>
        <p className="text-sm text-gray-600 mt-2">
          Advanced ENS transfer operations for <strong>{ensName}</strong> using backend-prepared transactions.
        </p>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Wallet Connection */}
        {!isConnected ? (
          <div className="p-6 bg-gradient-to-br from-[#4A148C]/5 via-[#7B1FA2]/5 to-[#4A148C]/10 border border-[#B497D6]/30 rounded-2xl text-center">
            <div className="space-y-4">
              <div className="w-16 h-16 mx-auto bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] rounded-full flex items-center justify-center shadow-lg">
                <WalletIcon className="h-8 w-8 text-white" />
              </div>
              <div className="space-y-2">
                <h3 className="text-lg font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent">
                  Connect Your Wallet
                </h3>
                <p className="text-sm text-gray-600 max-w-md mx-auto">
                  Connect your wallet to access ENS transfer functions. This allows you to create registrar contracts and transfer ENS names.
                </p>
              </div>
              <div className="flex justify-center">
                <ConnectButton />
              </div>
              <div className="flex items-center justify-center gap-4 text-xs text-gray-500">
                <div className="flex items-center gap-1">
                  <CheckCircleIcon className="h-3 w-3 text-green-600" />
                  <span>Mainnet & Testnet Support</span>
                </div>
                <div className="flex items-center gap-1">
                  <CheckCircleIcon className="h-3 w-3 text-green-600" />
                  <span>Secure Connection</span>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <>
            {/* ENS Name Selection */}
            <ENSNameSelector
              selectedENSName={selectedENSName}
              onENSNameSelect={handleENSNameSelect}
              onError={(error) => {
                setTransferError(error);
                onError?.(error);
              }}
              className="mb-6"
            />

            {/* Show transfer options only when ENS name is selected */}
            {selectedENSName && ensOwner && (
              <>
                {/* Current Owner Info */}
                <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg">
                  <div className="flex items-center gap-3">
                    <UserIcon className="h-5 w-5 text-blue-600" />
                    <div>
                      <p className="text-sm font-medium text-blue-800">Selected ENS: {selectedENSName}</p>
                      <code className="text-xs text-blue-700 bg-blue-100 px-2 py-1 rounded">
                        Owner: {ensOwner}
                      </code>
                    </div>
                  </div>
                </div>

        {/* Chain Selection */}
        <div className="space-y-2">
          <Label htmlFor="chain" className="text-sm font-medium text-[#4A148C]">
            Blockchain Network
          </Label>
          <Select
            value={selectedChain}
            onChange={(e) => setSelectedChain(e.target.value)}
            disabled={isTransferring}
            className="border-[#7B1FA2]/30 focus:border-[#4A148C] focus:ring-[#4A148C]/20"
          >
            <SelectOption value="sepolia">Sepolia Testnet</SelectOption>
            <SelectOption value="mainnet">Ethereum Mainnet</SelectOption>
            <SelectOption value="goerli">Goerli Testnet</SelectOption>
          </Select>
        </div>

        {/* Transfer Type Selection */}
        <div className="space-y-2">
          <Label className="text-sm font-medium text-[#4A148C]">
            Transfer Operation
          </Label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <Button
              onClick={() => setTransferType('registrar')}
              variant={transferType === 'registrar' ? 'default' : 'outline'}
              className={`h-auto p-4 text-left ${
                transferType === 'registrar'
                  ? 'bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white'
                  : 'border-[#7B1FA2]/30 hover:bg-[#7B1FA2]/5'
              }`}
              disabled={isTransferring}
            >
              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <SettingsIcon className="h-4 w-4" />
                  <span className="font-medium">Create Registrar</span>
                </div>
                <p className="text-xs opacity-80">
                  Create a subname registrar contract
                </p>
              </div>
            </Button>

            <Button
              onClick={() => setTransferType('namewrapper')}
              variant={transferType === 'namewrapper' ? 'default' : 'outline'}
              className={`h-auto p-4 text-left ${
                transferType === 'namewrapper'
                  ? 'bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white'
                  : 'border-[#7B1FA2]/30 hover:bg-[#7B1FA2]/5'
              }`}
              disabled={isTransferring}
            >
              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <ArrowRightIcon className="h-4 w-4" />
                  <span className="font-medium">NameWrapper Transfer</span>
                </div>
                <p className="text-xs opacity-80">
                  Transfer wrapped ENS name
                </p>
              </div>
            </Button>
          </div>
        </div>

        {/* NameWrapper Transfer - New Owner Input */}
        {transferType === 'namewrapper' && (
          <div className="space-y-2">
            <Label htmlFor="newOwner" className="text-sm font-medium text-[#4A148C]">
              New Owner Address
            </Label>
            <Input
              id="newOwner"
              type="text"
              placeholder="0x..."
              value={newOwnerAddress}
              onChange={(e) => handleAddressChange(e.target.value)}
              className={`border-[#7B1FA2]/30 focus:border-[#4A148C] focus:ring-[#4A148C]/20 ${
                addressError ? 'border-red-500' : ''
              }`}
              disabled={isTransferring}
            />
            {addressError && (
              <p className="text-sm text-red-600 flex items-center gap-1">
                <AlertCircleIcon className="h-4 w-4" />
                {addressError}
              </p>
            )}
          </div>
        )}

        {/* Warning */}
        <div className="p-4 bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200 rounded-lg">
          <div className="flex items-start gap-3">
            <AlertCircleIcon className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" />
            <div className="text-sm">
              <p className="text-amber-800 font-medium mb-1">Important Information</p>
              <p className="text-amber-700">
                {transferType === 'registrar'
                  ? 'Creating a registrar contract will allow subname management for this ENS domain. This operation requires gas fees.'
                  : 'NameWrapper transfers are irreversible. Once transferred, you will lose control of this ENS domain. Make sure the new owner address is correct.'
                }
              </p>
            </div>
          </div>
        </div>

        {/* Transfer Error */}
        {transferError && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-start gap-3">
              <AlertCircleIcon className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm">
                <p className="text-red-800 font-medium">Operation Failed</p>
                <p className="text-red-700">{transferError}</p>
              </div>
            </div>
          </div>
        )}

        {/* Success Message */}
        {transferStatus === 'success' && txHash && (
          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-start gap-3">
              <CheckCircleIcon className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm flex-1">
                <p className="text-green-800 font-medium mb-1">Operation Successful!</p>
                <p className="text-green-700 mb-2">
                  {transferType === 'registrar'
                    ? `Registrar contract for ${ensName} has been created successfully.`
                    : `${ensName} has been transferred via NameWrapper successfully.`
                  }
                </p>
                <Button
                  onClick={() => window.open(`https://etherscan.io/tx/${txHash}`, '_blank')}
                  size="sm"
                  variant="outline"
                  className="border-green-300 hover:bg-green-100 text-green-700"
                >
                  <ExternalLinkIcon className="mr-1 h-3 w-3" />
                  View Transaction
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Action Button */}
        <Button
          onClick={transferType === 'registrar' ? handleCreateRegistrar : handleNameWrapperTransfer}
          disabled={
            transferType === 'registrar'
              ? !isRegistrarFormValid || isTransferring || transferStatus === 'success'
              : !isNameWrapperFormValid || isTransferring || transferStatus === 'success'
          }
          className={`w-full transition-all duration-300 ${
            (transferType === 'registrar' ? !isRegistrarFormValid : !isNameWrapperFormValid) ||
            isTransferring ||
            transferStatus === 'success'
              ? 'opacity-50 cursor-not-allowed'
              : 'bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] hover:from-[#5A1A9C] hover:to-[#8B2FAC] text-white shadow-lg hover:shadow-xl transform hover:scale-105'
          }`}
        >
          {transferStatus === 'preparing' ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
              Preparing Transaction...
            </>
          ) : transferStatus === 'confirming' ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
              Confirm in Wallet...
            </>
          ) : transferStatus === 'pending' ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
              Transaction Pending...
            </>
          ) : transferStatus === 'success' ? (
            <>
              <CheckCircleIcon className="w-4 h-4 mr-2" />
              Operation Complete
            </>
          ) : (
            <>
              {transferType === 'registrar' ? (
                <>
                  <SettingsIcon className="w-4 h-4 mr-2" />
                  Create Registrar Contract
                </>
              ) : (
                <>
                  <ShieldCheckIcon className="w-4 h-4 mr-2" />
                  Transfer via NameWrapper
                </>
              )}
            </>
          )}
        </Button>

                {/* Transaction Hash */}
                {txHash && transferStatus === 'pending' && (
                  <div className="text-center">
                    <p className="text-sm text-gray-600 mb-2">Transaction Hash:</p>
                    <code className="text-xs bg-gray-100 px-2 py-1 rounded break-all">
                      {txHash}
                    </code>
                  </div>
                )}
              </>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}
