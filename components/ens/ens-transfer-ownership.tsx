'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/lib/toast-context";
import { useAccount } from 'wagmi';
import { transferENSOwnership } from "@/lib/ens-utils";
import { ethers } from 'ethers';
import { 
  ArrowRightIcon, 
  AlertCircleIcon, 
  CheckCircleIcon,
  ExternalLinkIcon,
  UserIcon,
  ShieldCheckIcon
} from 'lucide-react';

export interface ENSTransferOwnershipProps {
  ensName: string;
  currentOwner: string;
  onSuccess?: (txHash: string, newOwner: string) => void;
  onError?: (error: string) => void;
  className?: string;
}

type TransferStatus = 'idle' | 'validating' | 'confirming' | 'pending' | 'success' | 'error';

export function ENSTransferOwnership({
  ensName,
  currentOwner,
  onSuccess,
  onError,
  className = ""
}: ENSTransferOwnershipProps) {
  const { address, isConnected } = useAccount();
  const { showToast } = useToast();

  const [newOwnerAddress, setNewOwnerAddress] = useState('');
  const [transferStatus, setTransferStatus] = useState<TransferStatus>('idle');
  const [transferError, setTransferError] = useState<string | null>(null);
  const [txHash, setTxHash] = useState<string | null>(null);
  const [addressError, setAddressError] = useState<string | null>(null);

  // Validate Ethereum address
  const validateAddress = (address: string): boolean => {
    if (!address) {
      setAddressError('Address is required');
      return false;
    }

    if (!ethers.isAddress(address)) {
      setAddressError('Invalid Ethereum address format');
      return false;
    }

    if (address.toLowerCase() === currentOwner.toLowerCase()) {
      setAddressError('New owner cannot be the same as current owner');
      return false;
    }

    setAddressError(null);
    return true;
  };

  // Handle address input change
  const handleAddressChange = (value: string) => {
    setNewOwnerAddress(value);
    if (value) {
      validateAddress(value);
    } else {
      setAddressError(null);
    }
  };

  // Handle transfer ownership
  const handleTransferOwnership = async () => {
    if (!isConnected || !address) {
      setTransferError('Please connect your wallet');
      return;
    }

    if (!validateAddress(newOwnerAddress)) {
      return;
    }

    // Check if current user is the owner
    if (address.toLowerCase() !== currentOwner.toLowerCase()) {
      setTransferError('Only the current owner can transfer ownership');
      return;
    }

    setTransferStatus('validating');
    setTransferError(null);

    try {
      setTransferStatus('confirming');
      
      console.log('Transferring ENS ownership:', {
        ensName,
        from: currentOwner,
        to: newOwnerAddress
      });

      // Call the transfer function
      const tx = await transferENSOwnership(ensName, newOwnerAddress);
      
      setTransferStatus('pending');
      setTxHash(tx.hash);

      showToast({
        type: 'info',
        title: 'Transfer Initiated',
        description: 'Transaction submitted. Waiting for confirmation...'
      });

      // Wait for transaction confirmation
      const receipt = await tx.wait();
      
      if (receipt?.status === 1) {
        setTransferStatus('success');
        
        showToast({
          type: 'success',
          title: 'Ownership Transferred',
          description: `${ensName} ownership has been transferred successfully`
        });

        onSuccess?.(tx.hash, newOwnerAddress);
      } else {
        throw new Error('Transaction failed');
      }

    } catch (error: any) {
      console.error('ENS transfer error:', error);
      setTransferStatus('error');
      
      let errorMessage = 'Failed to transfer ownership';
      
      if (error.code === 'ACTION_REJECTED') {
        errorMessage = 'Transaction was rejected by user';
      } else if (error.message?.includes('insufficient funds')) {
        errorMessage = 'Insufficient funds for gas fees';
      } else if (error.message) {
        errorMessage = error.message;
      }

      setTransferError(errorMessage);
      onError?.(errorMessage);

      showToast({
        type: 'error',
        title: 'Transfer Failed',
        description: errorMessage
      });
    }
  };

  const isFormValid = newOwnerAddress && !addressError && isConnected;
  const isTransferring = ['validating', 'confirming', 'pending'].includes(transferStatus);

  return (
    <Card className={`bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl hover:shadow-xl transition-all duration-300 ${className}`}>
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent flex items-center gap-2">
          <ArrowRightIcon className="h-5 w-5 text-[#4A148C]" />
          Transfer ENS Ownership
        </CardTitle>
        <p className="text-sm text-gray-600 mt-2">
          Transfer ownership of <strong>{ensName}</strong> to another Ethereum address.
        </p>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Current Owner Info */}
        <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg">
          <div className="flex items-center gap-3">
            <UserIcon className="h-5 w-5 text-blue-600" />
            <div>
              <p className="text-sm font-medium text-blue-800">Current Owner</p>
              <code className="text-xs text-blue-700 bg-blue-100 px-2 py-1 rounded">
                {currentOwner}
              </code>
            </div>
          </div>
        </div>

        {/* New Owner Input */}
        <div className="space-y-2">
          <Label htmlFor="newOwner" className="text-sm font-medium text-[#4A148C]">
            New Owner Address
          </Label>
          <Input
            id="newOwner"
            type="text"
            placeholder="0x..."
            value={newOwnerAddress}
            onChange={(e) => handleAddressChange(e.target.value)}
            className={`border-[#7B1FA2]/30 focus:border-[#4A148C] focus:ring-[#4A148C]/20 ${
              addressError ? 'border-red-500' : ''
            }`}
            disabled={isTransferring}
          />
          {addressError && (
            <p className="text-sm text-red-600 flex items-center gap-1">
              <AlertCircleIcon className="h-4 w-4" />
              {addressError}
            </p>
          )}
        </div>

        {/* Warning */}
        <div className="p-4 bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200 rounded-lg">
          <div className="flex items-start gap-3">
            <AlertCircleIcon className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" />
            <div className="text-sm">
              <p className="text-amber-800 font-medium mb-1">Important Warning</p>
              <p className="text-amber-700">
                This action is irreversible. Once transferred, you will lose control of this ENS domain. 
                Make sure the new owner address is correct.
              </p>
            </div>
          </div>
        </div>

        {/* Transfer Error */}
        {transferError && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-start gap-3">
              <AlertCircleIcon className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm">
                <p className="text-red-800 font-medium">Transfer Failed</p>
                <p className="text-red-700">{transferError}</p>
              </div>
            </div>
          </div>
        )}

        {/* Success Message */}
        {transferStatus === 'success' && txHash && (
          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-start gap-3">
              <CheckCircleIcon className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm flex-1">
                <p className="text-green-800 font-medium mb-1">Transfer Successful!</p>
                <p className="text-green-700 mb-2">
                  Ownership of {ensName} has been transferred to the new owner.
                </p>
                <Button
                  onClick={() => window.open(`https://etherscan.io/tx/${txHash}`, '_blank')}
                  size="sm"
                  variant="outline"
                  className="border-green-300 hover:bg-green-100 text-green-700"
                >
                  <ExternalLinkIcon className="mr-1 h-3 w-3" />
                  View Transaction
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Transfer Button */}
        <Button
          onClick={handleTransferOwnership}
          disabled={!isFormValid || isTransferring || transferStatus === 'success'}
          className={`w-full transition-all duration-300 ${
            !isFormValid || isTransferring || transferStatus === 'success'
              ? 'opacity-50 cursor-not-allowed'
              : 'bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] hover:from-[#5A1A9C] hover:to-[#8B2FAC] text-white shadow-lg hover:shadow-xl transform hover:scale-105'
          }`}
        >
          {transferStatus === 'validating' ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
              Validating...
            </>
          ) : transferStatus === 'confirming' ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
              Confirm in Wallet...
            </>
          ) : transferStatus === 'pending' ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
              Transfer Pending...
            </>
          ) : transferStatus === 'success' ? (
            <>
              <CheckCircleIcon className="w-4 h-4 mr-2" />
              Transfer Complete
            </>
          ) : (
            <>
              <ShieldCheckIcon className="w-4 h-4 mr-2" />
              Transfer Ownership
            </>
          )}
        </Button>

        {/* Transaction Hash */}
        {txHash && transferStatus === 'pending' && (
          <div className="text-center">
            <p className="text-sm text-gray-600 mb-2">Transaction Hash:</p>
            <code className="text-xs bg-gray-100 px-2 py-1 rounded break-all">
              {txHash}
            </code>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
