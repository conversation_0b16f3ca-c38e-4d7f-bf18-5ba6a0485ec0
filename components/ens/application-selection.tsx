'use client';

import { useState, useEffect, useCallback } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/lib/toast-context";
import { 
  CheckCircleIcon, 
  RefreshCwIcon, 
  AlertCircleIcon,
  GlobeIcon,
  PlusIcon,
  ExternalLinkIcon
} from "lucide-react";
import { apiService, ApplicationWithApiKey } from "@/lib/api";
import { useAuth } from "@/lib/auth-context";

export interface ApplicationSelectionProps {
  selectedApplicationId?: string;
  onApplicationSelect: (applicationId: string, application: ApplicationWithApiKey) => void;
  onError?: (error: string) => void;
  className?: string;
  showCreateButton?: boolean;
}

export function ApplicationSelection({ 
  selectedApplicationId, 
  onApplicationSelect, 
  onError, 
  className = "",
  showCreateButton = true
}: ApplicationSelectionProps) {
  const { showToast } = useToast();
  const { token } = useAuth();
  
  const [applications, setApplications] = useState<ApplicationWithApiKey[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchApplications = useCallback(async () => {
    if (!token) {
      setError('Authentication required');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      const response = await apiService.getApplications(token);
      
      if (response.success && response.data) {
        const apps = Array.isArray(response.data) ? response.data : [];
        setApplications(apps);
        
        // Auto-select first application if none is selected and applications exist
        if (apps.length > 0 && !selectedApplicationId) {
          onApplicationSelect(apps[0].appId, apps[0]);
        }
      } else {
        const errorMessage = response.error || 'Failed to fetch applications';
        setError(errorMessage);
        onError?.(errorMessage);
      }
    } catch (err) {
      const errorMessage = 'Failed to fetch applications';
      setError(errorMessage);
      onError?.(errorMessage);
      console.error('Error fetching applications:', err);
    } finally {
      setLoading(false);
    }
  }, [token, selectedApplicationId, onApplicationSelect, onError]);

  useEffect(() => {
    fetchApplications();
  }, [fetchApplications]);

  const handleApplicationSelect = (app: ApplicationWithApiKey) => {
    onApplicationSelect(app.appId, app);
    showToast({
      type: 'success',
      title: 'Application Selected',
      description: `Selected ${app.name} for ENS integration`
    });
  };

  const handleRefresh = () => {
    fetchApplications();
  };

  const handleCreateApplication = () => {
    // Navigate to applications page to create new app
    window.open('/dashboard/applications', '_blank');
  };

  if (loading) {
    return (
      <Card className={`p-4 sm:p-6 bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl ${className}`}>
        <CardContent className="flex items-center justify-center py-8">
          <div className="flex items-center gap-3">
            <RefreshCwIcon className="h-5 w-5 animate-spin text-[#4A148C]" />
            <span className="text-gray-600">Loading applications...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={`p-4 sm:p-6 bg-white/80 backdrop-blur-sm border border-red-200 shadow-lg rounded-2xl ${className}`}>
        <CardContent className="py-6">
          <div className="flex items-center gap-3 mb-4">
            <AlertCircleIcon className="h-5 w-5 text-red-600" />
            <span className="text-red-800 font-medium">Failed to load applications</span>
          </div>
          <p className="text-red-700 mb-4">{error}</p>
          <Button
            onClick={handleRefresh}
            variant="outline"
            className="border-red-300 text-red-700 hover:bg-red-50"
          >
            <RefreshCwIcon className="h-4 w-4 mr-2" />
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (applications.length === 0) {
    return (
      <Card className={`p-4 sm:p-6 bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl ${className}`}>
        <CardHeader className="pb-4">
          <CardTitle className="text-lg sm:text-xl font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent flex items-center gap-2">
            <GlobeIcon className="h-5 w-5 sm:h-6 sm:w-6 text-[#4A148C]" />
            Select Application
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center py-6">
          <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] rounded-full flex items-center justify-center">
            <GlobeIcon className="w-8 h-8 text-white" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No Applications Found</h3>
          <p className="text-gray-600 mb-6">
            You need to create an application before you can register ENS domains.
          </p>
          {showCreateButton && (
            <Button
              onClick={handleCreateApplication}
              className="bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white hover:shadow-lg hover:shadow-[#4A148C]/25 hover:scale-105 transition-all duration-300"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Create Application
              <ExternalLinkIcon className="h-4 w-4 ml-2" />
            </Button>
          )}
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`p-4 sm:p-6 bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl ${className}`}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg sm:text-xl font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent flex items-center gap-2">
            <GlobeIcon className="h-5 w-5 sm:h-6 sm:w-6 text-[#4A148C]" />
            Select Application
          </CardTitle>
          <Button
            onClick={handleRefresh}
            variant="outline"
            size="sm"
            className="border-[#7B1FA2]/30 text-[#4A148C] hover:bg-[#7B1FA2]/5"
          >
            <RefreshCwIcon className="h-4 w-4" />
          </Button>
        </div>
        <p className="text-sm text-gray-600 mt-2">
          Choose which application you want to register ENS domains for.
        </p>
      </CardHeader>
      <CardContent className="space-y-3">
        {applications.map((app) => (
          <div
            key={app.appId}
            className={`p-4 rounded-xl border-2 cursor-pointer transition-all duration-200 ${
              selectedApplicationId === app.appId
                ? 'border-[#4A148C] bg-gradient-to-r from-[#4A148C]/5 to-[#7B1FA2]/5 shadow-md'
                : 'border-[#B497D6]/20 hover:border-[#7B1FA2]/40 hover:bg-[#7B1FA2]/5'
            }`}
            onClick={() => handleApplicationSelect(app)}
          >
            <div className="flex items-center justify-between">
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-3">
                  <h3 className="font-semibold text-gray-900 truncate">{app.name}</h3>
                  {selectedApplicationId === app.appId && (
                    <CheckCircleIcon className="h-5 w-5 text-[#4A148C] flex-shrink-0" />
                  )}
                </div>
                <p className="text-sm text-gray-600 mt-1">
                  App ID: <code className="text-xs bg-gray-100 px-1 py-0.5 rounded">{app.appId}</code>
                </p>
                {app.description && (
                  <p className="text-sm text-gray-500 mt-1 line-clamp-2">{app.description}</p>
                )}
              </div>
              <Badge 
                variant="default" 
                className="bg-green-100/80 text-green-800 border border-green-200 ml-3"
              >
                Active
              </Badge>
            </div>
          </div>
        ))}
        
        {showCreateButton && (
          <div className="pt-4 border-t border-[#B497D6]/20">
            <Button
              onClick={handleCreateApplication}
              variant="outline"
              className="w-full border-[#7B1FA2]/30 text-[#4A148C] hover:bg-[#7B1FA2]/5 hover:border-[#4A148C]"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Create New Application
              <ExternalLinkIcon className="h-4 w-4 ml-2" />
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
