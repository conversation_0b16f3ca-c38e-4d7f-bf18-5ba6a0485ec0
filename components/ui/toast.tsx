import * as React from "react"
import { cn } from "@/lib/utils"
import { CheckCircleIcon, XCircleIcon, AlertCircleIcon, InfoIcon, XIcon } from "lucide-react"

export interface ToastProps {
  id?: string
  title?: string
  description?: string
  type?: 'success' | 'error' | 'warning' | 'info'
  duration?: number
  onClose?: () => void
}

const Toast = React.forwardRef<HTMLDivElement, ToastProps>(
  ({ title, description, type = 'info', onClose, ...props }, ref) => {
    const [isVisible, setIsVisible] = React.useState(true)

    React.useEffect(() => {
      const timer = setTimeout(() => {
        setIsVisible(false)
        setTimeout(() => onClose?.(), 300)
      }, props.duration || 5000)

      return () => clearTimeout(timer)
    }, [onClose, props.duration])

    const icons = {
      success: CheckCircleIcon,
      error: XCircleIcon,
      warning: AlertCircleIcon,
      info: InfoIcon,
    }

    const Icon = icons[type]

    const variants = {
      success: "border-green-200 bg-green-50 text-green-800",
      error: "border-red-200 bg-red-50 text-red-800",
      warning: "border-yellow-200 bg-yellow-50 text-yellow-800",
      info: "border-blue-200 bg-blue-50 text-blue-800",
    }

    return (
      <div
        ref={ref}
        className={cn(
          "relative flex w-full items-start gap-3 rounded-lg border p-4 shadow-lg transition-all duration-300",
          variants[type],
          isVisible ? "opacity-100 translate-x-0" : "opacity-0 translate-x-full"
        )}
        {...props}
      >
        <Icon className="h-5 w-5 flex-shrink-0 mt-0.5" />
        <div className="flex-1 space-y-1">
          {title && (
            <div className="font-medium text-sm">{title}</div>
          )}
          {description && (
            <div className="text-sm opacity-90">{description}</div>
          )}
        </div>
        {onClose && (
          <button
            onClick={() => {
              setIsVisible(false)
              setTimeout(() => onClose(), 300)
            }}
            className="flex-shrink-0 opacity-70 hover:opacity-100 transition-opacity"
          >
            <XIcon className="h-4 w-4" />
          </button>
        )}
      </div>
    )
  }
)
Toast.displayName = "Toast"

export { Toast }