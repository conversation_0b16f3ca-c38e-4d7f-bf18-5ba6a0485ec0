import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"

interface FormFieldProps {
  label: string
  id: string
  type?: string
  placeholder?: string
  required?: boolean
  value?: string
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void
}

export function FormField({ 
  label, 
  id, 
  type = "text", 
  placeholder, 
  required = false,
  value,
  onChange
}: FormFieldProps) {
  return (
    <div className="space-y-2">
      <Label htmlFor={id}>{label}</Label>
      <Input 
        id={id} 
        type={type} 
        placeholder={placeholder} 
        required={required}
        value={value}
        onChange={onChange}
      />
    </div>
  )
}