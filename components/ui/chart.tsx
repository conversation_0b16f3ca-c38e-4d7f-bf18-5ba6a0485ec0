"use client"

import * as React from "react"
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer 
} from 'recharts'

interface ChartProps {
  data: Array<{ name: string; [key: string]: number | string }>
  metrics: Array<{
    name: string
    color: string
    type: 'bar'
  }>
}

export function Chart({ data, metrics }: ChartProps) {
  return (
    <ResponsiveContainer width="100%" height={300}>
      <BarChart data={data}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="name" />
        <YAxis />
        <Tooltip />
        <Legend />
        {metrics.map(metric => (
          <Bar 
            key={metric.name} 
            dataKey={metric.name} 
            fill={metric.color} 
            activeBar={<span className="fill-accent" />}
          />
        ))}
      </BarChart>
    </ResponsiveContainer>
  )
}