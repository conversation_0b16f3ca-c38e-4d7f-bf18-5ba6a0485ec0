import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const alertVariants = cva(
  "relative w-full rounded-2xl border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground backdrop-blur-sm",
  {
    variants: {
      variant: {
        default: "bg-white/80 text-gray-900 border-gray-200",
        destructive:
          "border-red-200 bg-red-50/80 text-red-800 [&>svg]:text-red-600",
        warning:
          "border-orange-200 bg-orange-50/80 text-orange-800 [&>svg]:text-orange-600",
        success:
          "border-green-200 bg-green-50/80 text-green-800 [&>svg]:text-green-600",
        info:
          "border-[#B497D6]/30 bg-[#B497D6]/10 text-[#4A148C] [&>svg]:text-[#4A148C]",
        purple:
          "border-[#7B1FA2]/30 bg-gradient-to-r from-[#4A148C]/10 to-[#7B1FA2]/10 text-[#4A148C] [&>svg]:text-[#4A148C]",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

const Alert = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>
>(({ className, variant, ...props }, ref) => (
  <div
    ref={ref}
    role="alert"
    className={cn(alertVariants({ variant }), className)}
    {...props}
  />
))
Alert.displayName = "Alert"

const AlertTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h5
    ref={ref}
    className={cn("mb-1 font-medium leading-none tracking-tight", className)}
    {...props}
  />
))
AlertTitle.displayName = "AlertTitle"

const AlertDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("text-sm [&_p]:leading-relaxed", className)}
    {...props}
  />
))
AlertDescription.displayName = "AlertDescription"

export { Alert, AlertTitle, AlertDescription }
