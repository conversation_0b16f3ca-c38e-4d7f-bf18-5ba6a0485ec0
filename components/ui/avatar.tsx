import * as React from "react"
import Image from "next/image"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const avatarVariants = cva(
  "relative flex h-10 w-10 shrink-0 overflow-hidden rounded-2xl",
  {
    variants: {
      variant: {
        default: "",
        circle: "rounded-full",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

const AvatarImage = React.forwardRef<
  HTMLImageElement,
  React.ImgHTMLAttributes<HTMLImageElement> & 
  VariantProps<typeof avatarVariants> & {
    width?: number;
    height?: number;
    src: string;
  }
>(({ className, alt, width = 40, height = 40, src, ...props }, ref) => (
  <Image
    ref={ref}
    alt={alt || "Avatar"}
    width={width}
    height={height}
    src={src}
    className={cn("aspect-square h-full w-full object-cover", className)}
    {...props}
  />
))
AvatarImage.displayName = "AvatarImage"

const AvatarFallback = React.forwardRef<
  HTMLSpanElement,
  React.HTMLAttributes<HTMLSpanElement> & 
  VariantProps<typeof avatarVariants>
>(({ className, ...props }, ref) => (
  <span
    ref={ref}
    className={cn(
      "flex h-full w-full items-center justify-center rounded-2xl bg-muted text-xs font-medium",
      className
    )}
    {...props}
  />
))
AvatarFallback.displayName = "AvatarFallback"

interface AvatarProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof avatarVariants> {}

const Avatar = React.forwardRef<HTMLDivElement, AvatarProps>(
  ({ className, variant, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(avatarVariants({ variant, className }))}
        {...props}
      />
    )
  }
)
Avatar.displayName = "Avatar"

export { Avatar, AvatarImage, AvatarFallback }