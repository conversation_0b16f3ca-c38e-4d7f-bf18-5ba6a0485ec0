import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-2xl text-sm font-medium ring-offset-background transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-[#4A148C]/50 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white hover:shadow-lg hover:shadow-[#4A148C]/25 hover:scale-105",
        destructive:
          "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        outline:
          "border border-[#7B1FA2]/30 bg-white hover:bg-[#7B1FA2]/5 hover:border-[#4A148C] hover:text-[#4A148C]",
        secondary:
          "bg-[#B497D6]/10 text-[#4A148C] hover:bg-[#B497D6]/20 border border-[#B497D6]/20",
        ghost: "hover:bg-[#7B1FA2]/10 hover:text-[#4A148C]",
        link: "text-[#4A148C] underline-offset-4 hover:underline hover:text-[#7B1FA2]",
        purple: "bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white hover:shadow-lg hover:shadow-[#4A148C]/25 hover:scale-105",
        "purple-outline": "border-2 border-[#4A148C] text-[#4A148C] bg-white hover:bg-gradient-to-r hover:from-[#4A148C] hover:to-[#7B1FA2] hover:text-white hover:shadow-lg",
      },
      size: {
        default: "h-10 px-4 py-2 min-h-[44px] sm:min-h-[40px]",
        sm: "h-9 rounded-xl px-3 min-h-[36px] sm:min-h-[32px]",
        lg: "h-12 rounded-xl px-8 min-h-[48px] sm:min-h-[44px]",
        icon: "h-10 w-10 min-h-[44px] min-w-[44px] sm:min-h-[40px] sm:min-w-[40px]",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }