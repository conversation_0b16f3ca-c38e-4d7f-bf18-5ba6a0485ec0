import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center rounded-2xl border px-2.5 py-0.5 text-xs font-semibold transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-[#4A148C]/50 focus:ring-offset-2",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white hover:shadow-md hover:shadow-[#4A148C]/25",
        secondary:
          "border-transparent bg-[#B497D6]/10 text-[#4A148C] hover:bg-[#B497D6]/20",
        destructive:
          "border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",
        outline: "border-[#7B1FA2]/30 text-[#4A148C] hover:bg-[#7B1FA2]/5",
        purple: "border-transparent bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white",
        "purple-light": "border-transparent bg-[#B497D6]/20 text-[#4A148C]",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

const Badge = React.forwardRef<HTMLDivElement, BadgeProps>(
  ({ className, variant, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(badgeVariants({ variant, className }))}
        {...props}
      />
    )
  }
)
Badge.displayName = "Badge"

export { Badge }