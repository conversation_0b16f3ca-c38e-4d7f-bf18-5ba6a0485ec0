// components/ui/card-content.tsx
import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

// Define variants for card content
const cardContentVariants = cva(
  "p-6 pt-0 text-card-foreground transition-colors",
  {
    variants: {
      variant: {
        default: "",
        compact: "p-4 pt-0",
        flush: "p-0",
      },
      radius: {
        default: "rounded-b-2xl",
        none: "rounded-none",
      },
    },
    defaultVariants: {
      variant: "default",
      radius: "default",
    },
  }
)

export interface CardContentProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof cardContentVariants> {}

const CardContent = React.forwardRef<HTMLDivElement, CardContentProps>(
  ({ className, variant, radius, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(cardContentVariants({ variant, radius, className }))}
        {...props}
      />
    )
  }
)

CardContent.displayName = "CardContent"

export { CardContent, cardContentVariants }