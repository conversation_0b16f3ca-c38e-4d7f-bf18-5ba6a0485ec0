import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

interface LogEntry {
  id: number
  timestamp: string
  level: "info" | "warning" | "error"
  message: string
}

interface LogsViewerProps {
  logs: LogEntry[]
}

export function LogsViewer({ logs }: LogsViewerProps) {
  const getLevelColor = (level: string) => {
    switch (level) {
      case "error": return "bg-destructive text-destructive-foreground"
      case "warning": return "bg-yellow-500 text-yellow-900"
      default: return "bg-muted text-muted-foreground"
    }
  }

  return (
    <Card className="p-4">
      <div className="space-y-4">
        {logs.map((log) => (
          <div key={log.id} className="p-3 border rounded-lg">
            <div className="flex items-center justify-between">
              <Badge className={getLevelColor(log.level)}>{log.level}</Badge>
              <span className="text-xs text-muted-foreground">{log.timestamp}</span>
            </div>
            <p className="mt-2 text-sm">{log.message}</p>
          </div>
        ))}
      </div>
    </Card>
  )
}