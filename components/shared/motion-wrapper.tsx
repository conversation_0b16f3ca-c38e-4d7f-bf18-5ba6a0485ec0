import { motion } from "framer-motion"
import { memo } from "react"

// Memoized motion variants for better performance
const fadeInVariants = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.5 }
}

const slideInVariants = {
  initial: { opacity: 0, x: -20 },
  animate: { opacity: 1, x: 0 },
  transition: { duration: 0.4 }
}

const scaleInVariants = {
  initial: { opacity: 0, scale: 0.95 },
  animate: { opacity: 1, scale: 1 },
  transition: { duration: 0.3 }
}

interface MotionWrapperProps {
  children: React.ReactNode
  variant?: 'fadeIn' | 'slideIn' | 'scaleIn'
  delay?: number
}

export const MotionWrapper = memo(function MotionWrapper({
  children,
  variant = 'fadeIn',
  delay = 0
}: MotionWrapperProps) {
  const variants = {
    fadeIn: fadeInVariants,
    slideIn: slideInVariants,
    scaleIn: scaleInVariants
  }

  const selectedVariant = variants[variant]

  return (
    <motion.div
      initial={selectedVariant.initial}
      animate={selectedVariant.animate}
      transition={{ ...selectedVariant.transition, delay }}
    >
      {children}
    </motion.div>
  )
})