import { Dialog, DialogContent, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"

interface ModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  title: string
  children: React.ReactNode
  actionLabel?: string
  onAction?: () => void
  isLoading?: boolean
}

export function Modal({ 
  open, 
  onOpenChange, 
  title, 
  children, 
  actionLabel = "Save",
  onAction,
  isLoading = false
}: ModalProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>
        {children}
        <div className="flex justify-end gap-3 mt-6">
          <Button variant="outline" onClick={() => onOpenChange(false)}>Cancel</Button>
          <Button onClick={onAction} disabled={isLoading}>
            {isLoading ? "Saving..." : actionLabel}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}