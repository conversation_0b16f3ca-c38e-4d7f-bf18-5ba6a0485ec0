import { Button } from "@/components/ui/button"

interface RoleSelectorProps {
  selectedRole: string
  onSelectRole: (role: string) => void
}

export function RoleSelector({ selectedRole, onSelectRole }: RoleSelectorProps) {
  return (
    <div className="grid grid-cols-2 gap-2 p-1 bg-muted/50 rounded-lg">
      <Button
        variant={selectedRole === 'developer' ? 'default' : 'outline'}
        onClick={() => onSelectRole('developer')}
        className="rounded-md"
      >
        Developer
      </Button>
      <Button
        variant={selectedRole === 'admin' ? 'default' : 'outline'}
        onClick={() => onSelectRole('admin')}
        className="rounded-md"
      >
        Admin
      </Button>
    </div>
  )
}