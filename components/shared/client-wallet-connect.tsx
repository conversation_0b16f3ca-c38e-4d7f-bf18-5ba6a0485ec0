'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { WalletIcon } from 'lucide-react';

interface WalletConnectProps {
  showDisconnect?: boolean;
  className?: string;
  onConnect?: (address: string) => void;
  onDisconnect?: () => void;
}

export function WalletConnect(props: WalletConnectProps) {
  const [mounted, setMounted] = useState(false);
  const [WalletConnectComponent, setWalletConnectComponent] = useState<any>(null);

  useEffect(() => {
    setMounted(true);
    // Dynamically import the wallet connect component only on client side
    import('./wallet-connect').then(mod => {
      setWalletConnectComponent(() => mod.WalletConnect);
    });
  }, []);

  if (!mounted || !WalletConnectComponent) {
    return (
      <Button disabled className="bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white">
        <WalletIcon className="h-4 w-4 mr-2" />
        Loading...
      </Button>
    );
  }

  return <WalletConnectComponent {...props} />;
}

export function useWalletConnection() {
  const [mounted, setMounted] = useState(false);
  const [hookResult, setHookResult] = useState({
    address: undefined,
    isConnected: false,
    isConnecting: false,
  });

  useEffect(() => {
    setMounted(true);
    if (typeof window !== 'undefined') {
      // Dynamically import and use the hook only on client side
      import('./wallet-connect').then(mod => {
        try {
          const result = mod.useWalletConnection();
          setHookResult(result);
        } catch (error) {
          console.warn('Failed to use wallet connection hook:', error);
        }
      });
    }
  }, []);

  return hookResult;
}
