'use client';

import { motion, Variants } from 'framer-motion';

const featuresData = [
	{
		icon: '🔐',
		title: 'Credential Management',
		desc: 'Securely issue, store, and verify digital credentials with cutting-edge cryptography.',
	},
	{
		icon: '⚡',
		title: 'Real-time API Access',
		desc: 'Integrate seamlessly with our powerful APIs for instant identity verification and data exchange.',
	},
	{
		icon: '💳',
		title: 'Wallet Integration',
		desc: 'Connect with a wide range of Web3 wallets, offering users flexibility and control.',
	},
	{
		icon: '🧩',
		title: 'Modular SDKs',
		desc: 'Flexible and developer-friendly SDKs to build customized identity solutions quickly.',
	},
	{
		icon: '🛡️',
		title: 'Zero-Knowledge Proofs',
		desc: 'Enable privacy-preserving verification, ensuring data security and user anonymity.',
	},
	{
		icon: '🌐',
		title: 'Decentralized Identifiers',
		desc: 'Support for DIDs and verifiable credentials, building a truly interoperable identity layer.',
	},
];

const sectionVariants: Variants = {
	hidden: { opacity: 0 },
	visible: {
		opacity: 1,
		transition: {
			staggerChildren: 0.2,
		},
	},
};

const cardVariants: Variants = {
	hidden: { y: 30, opacity: 0 },
	visible: {
		y: 0,
		opacity: 1,
		transition: {
			duration: 0.5,
			ease: "easeOut",
		},
	},
};

const FeaturesSection = () => {
	return (
		<section id="features" className="px-4 md:px-12 py-16 bg-white">
			<div className="max-w-6xl mx-auto text-center">
				<motion.h2
					className="text-4xl lg:text-5xl font-heading font-bold mb-4 text-black"
					initial={{ opacity: 0, y: -20 }}
					whileInView={{ opacity: 1, y: 0 }}
					viewport={{ once: true, amount: 0.3 }}
					transition={{ duration: 0.5 }}
				>
					Powerful Features, Seamlessly Integrated
				</motion.h2>
				<motion.p
					className="text-lg text-black mb-10 max-w-2xl mx-auto"
					initial={{ opacity: 0, y: -20 }}
					whileInView={{ opacity: 1, y: 0 }}
					viewport={{ once: true, amount: 0.3 }}
					transition={{ duration: 0.5, delay: 0.2 }}
				>
					Crefy Connect provides a comprehensive suite of tools to build
					next-generation applications with robust identity solutions.
				</motion.p>

				<motion.div
					className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
					variants={sectionVariants}
					initial="hidden"
					whileInView="visible"
					viewport={{ once: true, amount: 0.1 }}
				>
					{featuresData.map((feature, index) => (
						<motion.div
							key={index}
							className="
                bg-white border border-[#e0e0e0] rounded-xl p-6
                hover:border-[#d1c4e9] hover:shadow-lg transition-all duration-300
                transform hover:scale-105
              "
							variants={cardVariants}
						>
							<div className="text-4xl mb-4">{feature.icon}</div>
							<h3 className="text-2xl font-heading font-semibold mb-3 text-[#4a148c]">
								{feature.title}
							</h3>
							<p className="text-gray-600 leading-relaxed">
								{feature.desc}
							</p>
						</motion.div>
					))}
				</motion.div>
			</div>
		</section>
	);
};

export default FeaturesSection;