'use client';

import { motion, useScroll, useTransform, Variants } from 'framer-motion';
import { useRef } from 'react';
import Image from 'next/image';

// Updated colors to deep purple and royal purple
const timelineData = [
  {
    image: "/images/t1.jpg",
    title: "Connect Your Wallet",
    description: "Easily link your preferred Web3 wallet to start interacting with the Crefy Connect platform in seconds.",
    color: "from-purple-900 to-purple-700", // Deep purple to royal purple
  },
  {
    image: "/images/t2.jpg",
    title: "Issue & Store Credentials",
    description: "Securely generate, issue, and manage verifiable credentials for users or applications.",
    color: "from-purple-800 to-purple-600", // Deep-royal purple transition
  },
  {
    image: "/images/t3.jpg",
    title: "Access & Verify APIs",
    description: "Utilize our robust API endpoints to verify credentials, manage identities, and build secure workflows.",
    color: "from-purple-700 to-purple-800", // Royal to deep purple
  },
  {
    image: "/images/t4.jpg",
    title: "Manage Sessions & Permissions",
    description: "Control access, manage user sessions, and define granular permissions for your applications.",
    color: "from-purple-600 to-purple-900", // Royal to deep purple
  },
];

const TimelineSection = () => {
  const sectionRef = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: sectionRef
  });

  const sectionTitleVariants: Variants = {
    hidden: { opacity: 0, y: -20 },
    visible: { 
      opacity: 1, 
      y: 0, 
      transition: { 
        duration: 0.5, 
        ease: "easeOut" 
      } 
    }
  };

  // Enhanced slide in/out animation variants with alternating directions
  const itemVariants: Variants = {
    hidden: (index) => ({ 
      opacity: 0, 
      x: index % 2 === 0 ? -70 : 70 // Alternate between sliding from left and right
    }),
    visible: (i) => ({ 
      opacity: 1,
      x: 0,
      transition: {
        delay: i * 0.3,
        duration: 0.8,
        ease: "easeOut"
      }
    })
  };

  // Parallax effect for timeline line
  const lineHeight = useTransform(scrollYProgress, [0, 1], ["0%", "100%"]);

  return (
    <section
      id="how-it-works"
      ref={sectionRef}
      className="bg-white px-4 md:px-12 py-16"
    >
      <div className="max-w-4xl mx-auto">
        <motion.div
          className="text-center mb-10"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
          variants={sectionTitleVariants}
        >
          <h2 className="text-4xl lg:text-5xl font-heading font-bold text-black-900">
            How It Works
          </h2>
          <p className="text-lg text-black-700 mt-4 max-w-2xl mx-auto">
            A simple, step-by-step guide to integrating and utilizing Crefy Connect&apos;s powerful identity infrastructure.
          </p>
        </motion.div>

        <div className="relative">
          {/* Vertical line acting as the timeline axis with animated progress */}
          <div className="hidden md:block absolute top-0 bottom-0 left-1/2 w-1 bg-purple-200 -translate-x-1/2 h-full">
            <motion.div 
              className="w-full bg-gradient-to-b from-purple-900 to-purple-700"
              style={{ height: lineHeight }}
            />
          </div>

          {timelineData.map((item, index) => {
            const isEven = index % 2 === 0;
            return (
              <motion.div
                key={index}
                initial="hidden"
                whileInView="visible"
                viewport={{ once: false, amount: 0.3 }}
                variants={itemVariants}
                custom={index} // Pass index for staggered delay and direction
                className={`
                  mb-16 flex md:items-center
                  ${isEven ? 'md:flex-row' : 'md:flex-row-reverse'}
                `}
              >
                {/* Full image illustration */}
                <div className={`
                  relative flex-shrink-0 w-40 h-40 md:w-64 md:h-48
                  flex items-center justify-center shadow-lg
                  md:mx-auto z-10 overflow-hidden rounded-lg
                `}>
                  <Image 
                    src={item.image} 
                    alt={item.title} 
                    fill 
                    className="object-cover"
                  />
                </div>

                {/* Content with number on text side */}
                <motion.div 
                  className={`
                    bg-white border border-purple-100 rounded-xl p-6 shadow-xl
                    w-full md:w-2/5
                    ${isEven ? 'md:ml-4' : 'md:mr-4'}
                    mt-4 md:mt-0
                    relative
                  `}
                  whileHover={{ 
                    scale: 1.02, 
                    boxShadow: "0 20px 25px -5px rgba(76, 29, 149, 0.1), 0 10px 10px -5px rgba(76, 29, 149, 0.04)" 
                  }}
                  transition={{ type: "spring", stiffness: 400, damping: 10 }}
                >
                  <div className="flex items-center mb-2">
                    <span className="flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-br from-purple-900 to-purple-700 flex items-center justify-center text-white font-bold text-lg mr-2">
                      {index + 1}
                    </span>
                    <h3 className="text-2xl font-heading font-semibold text-purple-900">{item.title}</h3>
                  </div>
                  <p className="text-black-700 leading-relaxed">{item.description}</p>
                </motion.div>
              </motion.div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default TimelineSection;