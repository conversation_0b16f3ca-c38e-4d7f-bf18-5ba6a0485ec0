'use client';

import { motion, cubicBezier } from 'framer-motion';
import Link from 'next/link';
import { BookOpen, Github, Download, Users, Code, Terminal, FileCode, Coffee } from 'lucide-react';

// Define our deep purple and royal purple colors
// Deep Purple: #4A148C (or tailwind's purple-900)
// Royal Purple: #7B1FA2 (or a mix between purple-700 and purple-800)

const resourcesData = [
  {
    icon: BookOpen,
    title: "API Documentation",
    description: "Explore our comprehensive developer docs to get started with Crefy Connect APIs.",
    link: "/docs",
    gradient: "from-purple-900 to-purple-700",
    size: "wide", // wide card (spans 2 columns)
  },
  {
    icon: Github,
    title: "GitHub Repository",
    description: "Access our SDKs, examples, and contribute to the project on GitHub.",
    link: "https://github.com/your-org/crefy-connect",
    gradient: "from-purple-800 to-purple-600",
    size: "normal", // normal card
  },
  {
    icon: Download,
    title: "SDK Downloads",
    description: "Download our latest SDKs for various platforms and start building.",
    link: "/downloads",
    gradient: "from-purple-700 to-purple-900",
    size: "normal", // normal card
  },
  {
    icon: Users,
    title: "Community Hub",
    description: "Join our Discord server and forums to connect with other developers and our team.",
    link: "/community",
    gradient: "from-purple-900 to-purple-700",
    size: "tall", // tall card (spans 2 rows)
  },
  {
    icon: Code,
    title: "Code Examples",
    description: "Ready-to-use code snippets to jump-start your integration.",
    link: "/examples",
    gradient: "from-purple-800 to-purple-600",
    size: "normal", // normal card
  },
  {
    icon: Terminal,
    title: "CLI Tool",
    description: "Powerful command line interface for developers who prefer terminal workflows.",
    link: "/cli",
    gradient: "from-purple-700 to-purple-900",
    size: "large", // large card (spans 2 columns and 2 rows)
  },
  {
    icon: FileCode,
    title: "Sandbox Environment",
    description: "Test your integration in our safe sandbox environment before going live.",
    link: "/sandbox",
    gradient: "from-purple-800 to-purple-600",
    size: "wide", // wide card (spans 2 columns)
  },
  {
    icon: Coffee,
    title: "Developer Blog",
    description: "Stay updated with the latest features, tips, and best practices.",
    link: "/blog",
    gradient: "from-purple-900 to-purple-700",
    size: "normal", // normal card
  },
];

const ResourcesSection = () => {
  const sectionVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: { staggerChildren: 0.15, delayChildren: 0.2 },
    },
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 20, scale: 0.95 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: { 
        duration: 0.6, 
        ease: cubicBezier(0.22, 1, 0.36, 1) // Using cubicBezier function
      },
    },
  };

  return (
    <section
      id="resources"
      className="bg-white px-4 md:px-12 py-16"
    >
      <div className="max-w-7xl mx-auto flex flex-col items-center">
        <motion.div
          className="text-center mb-10"
          initial={{ opacity: 0, y: -20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.3 }}
          transition={{ duration: 0.7 }}
        >
          <h2 className="text-4xl lg:text-5xl font-heading font-bold text-black-900">
            Developer Resources
          </h2>
          <div className="h-1 w-24 bg-gradient-to-r from-purple-900 to-purple-700 mx-auto mt-6 rounded-full"></div>
          <p className="text-lg text-black-800/90 mt-6 max-w-2xl mx-auto">
            Everything you need to successfully build and integrate with Crefy Connect.
          </p>
        </motion.div>

        {/* Bento Grid Layout - Square-like aspect ratio */}
        <motion.div
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mx-auto w-full max-w-6xl"
          style={{ gridAutoRows: "minmax(180px, auto)" }}
          variants={sectionVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.05 }}
        >
          {resourcesData.map((resource, index) => {
            const IconComponent = resource.icon;
            
            // Determine col and row spans based on size
            let colSpan = '';
            let rowSpan = '';
            
            switch(resource.size) {
              case 'wide':
                colSpan = 'sm:col-span-2';
                break;
              case 'tall':
                rowSpan = 'sm:row-span-2';
                break;
              case 'large':
                colSpan = 'sm:col-span-2';
                rowSpan = 'sm:row-span-2';
                break;
              default: // normal size
                break;
            }
            
            // Alternate depth effect for cards
            const depth = index % 2 === 0 
              ? 'hover:shadow-[0_10px_40px_-15px_rgba(74,20,140,0.3)]' 
              : 'hover:shadow-[0_15px_30px_-10px_rgba(123,31,162,0.25)]';
            
            return (
              <motion.div 
                key={index} 
                variants={cardVariants} 
                className={`${colSpan} ${rowSpan}`}
                whileHover={{ y: -5, transition: { duration: 0.2 } }}
              >
                <Link 
                  href={resource.link}
                  target={resource.link.startsWith('http') ? '_blank' : '_self'}
                  rel={resource.link.startsWith('http') ? 'noopener noreferrer' : ''}
                  className={`
                    block h-full bg-white rounded-2xl p-5 md:p-6 lg:p-7
                    border border-purple-200 hover:border-purple-400
                    transition-all duration-300
                    ${depth}
                    group relative overflow-hidden
                    flex flex-col
                    ${resource.size === 'tall' || resource.size === 'large' ? 'justify-between' : ''}
                  `}
                >
                  {/* Custom background patterns based on card type */}
                  {resource.size === 'large' ? (
                    <div className="absolute top-0 right-0 w-full h-full">
                      <div className="absolute top-0 right-0 w-40 h-40 bg-gradient-to-bl from-purple-100 to-transparent opacity-40 rounded-full -mr-10 -mt-10"></div>
                      <div className="absolute bottom-0 left-0 w-32 h-32 bg-gradient-to-tr from-purple-50 to-transparent opacity-30 rounded-full -ml-8 -mb-8"></div>
                    </div>
                  ) : resource.size === 'wide' ? (
                    <div className="absolute top-0 right-0 w-full h-full">
                      <div className="absolute top-0 right-0 w-36 h-full bg-gradient-to-l from-purple-50 to-transparent opacity-30"></div>
                    </div>
                  ) : resource.size === 'tall' ? (
                    <div className="absolute top-0 right-0 w-full h-full">
                      <div className="absolute bottom-0 right-0 w-full h-40 bg-gradient-to-t from-purple-50 to-transparent opacity-30"></div>
                    </div>
                  ) : (
                    <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-bl from-purple-100 to-transparent opacity-40 rounded-full -mr-8 -mt-8"></div>
                  )}
                  
                  <div className="flex-1 flex flex-col">
                    <div className={`
                      relative z-10 w-16 h-16 rounded-xl bg-gradient-to-br ${resource.gradient}
                      flex items-center justify-center mb-4 shadow-md group-hover:shadow-lg
                      group-hover:scale-110 transition-all duration-300
                      ${resource.size === 'large' ? 'w-20 h-20 mb-5' : ''}
                    `}>
                      <IconComponent 
                        size={resource.size === 'large' ? 36 : resource.size === 'wide' || resource.size === 'tall' ? 28 : 24} 
                        strokeWidth={1.75} 
                        className="text-white" 
                      />
                    </div>
                    
                    <h3 className={`
                      font-heading font-bold mb-2 text-purple-900 group-hover:text-purple-800 
                      transition-colors duration-300 relative z-10
                      ${resource.size === 'large' ? 'text-2xl mb-3' : resource.size === 'wide' || resource.size === 'tall' ? 'text-xl' : 'text-lg'}
                      line-clamp-2
                    `}>
                      {resource.title}
                    </h3>
                    
                    <p className={`
                      text-slate-600 leading-relaxed relative z-10
                      ${resource.size === 'large' ? 'text-base' : 'text-sm'}
                      ${resource.size === 'large' ? 'mb-4' : resource.size === 'wide' || resource.size === 'tall' ? 'mb-3' : 'mb-2'}
                      ${resource.size === 'normal' ? 'line-clamp-3' : 'line-clamp-4'}
                    `}>
                      {resource.description}
                    </p>
                  </div>
                  
                  <div className={`
                    mt-auto pt-2 text-xs font-medium text-purple-700 flex items-center 
                    opacity-80 group-hover:opacity-100 transition-opacity duration-300 relative z-10
                    ${resource.size === 'large' ? 'text-sm pt-3' : ''}
                  `}>
                    <span>Learn more</span>
                    <svg xmlns="http://www.w3.org/2000/svg" className={`
                      h-3.5 w-3.5 ml-1 group-hover:ml-2 transition-all duration-300
                      ${resource.size === 'large' ? 'h-4 w-4' : ''}
                    `} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </Link>
              </motion.div>
            );
          })}
        </motion.div>
        
        {/* Additional CTA */}
        <motion.div 
          className="mt-10 text-center w-full"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ delay: 0.5, duration: 0.7 }}
        >
          <div className="inline-block px-8 py-4 rounded-full bg-gradient-to-r from-purple-900 to-purple-700 text-white font-medium text-sm cursor-pointer hover:shadow-lg hover:shadow-purple-200 transition-all duration-300">
            <Link href="/support" className="flex items-center">
              <span>Need help with integration?</span>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
              </svg>
            </Link>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default ResourcesSection;