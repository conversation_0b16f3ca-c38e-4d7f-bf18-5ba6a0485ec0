'use client';

import { motion, Variants } from 'framer-motion';
import Link from 'next/link';
import { RocketIcon, PlayCircleIcon, CodeIcon } from 'lucide-react';

const CTASection = () => {
  const sectionVariants: Variants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: { staggerChildren: 0.2, delayChildren: 0.1 },
    },
  };

  const itemVariants: Variants = {
    hidden: { opacity: 0, y: 30, scale: 0.95 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: { duration: 0.6, ease: [0.43, 0.13, 0.23, 0.96] } // cubic bezier approximation of easeInOut
    }
  };

  return (
    <section
      id="cta"
      className="bg-white px-4 md:px-12 py-16 relative overflow-hidden"
    >
      {/* Background decorative elements */}
      <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-bl from-purple-100 to-transparent opacity-30 rounded-full -mr-48 -mt-48"></div>
      <div className="absolute bottom-0 left-0 w-80 h-80 bg-gradient-to-tr from-purple-50 to-transparent opacity-30 rounded-full -ml-40 -mb-40"></div>
      
      <div className="max-w-7xl mx-auto flex flex-col items-center relative z-10">
        <motion.div
          className="text-center mb-10"
          initial={{ opacity: 0, y: -20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.3 }}
          transition={{ duration: 0.7 }}
        >
          <h2 className="text-4xl lg:text-5xl font-heading font-bold text-purple-900">
            Start Building in Minutes
          </h2>
          <div className="h-1 w-24 bg-gradient-to-r from-purple-900 to-purple-700 mx-auto mt-6 rounded-full"></div>
          <p className="text-lg text-slate-600 mt-6 max-w-2xl mx-auto">
            Ready to revolutionize identity management in your applications?
            Join our developer community or see Crefy Connect in action.
          </p>
        </motion.div>

        <motion.div
          className="grid grid-cols-1 md:grid-cols-3 gap-6 w-full max-w-5xl"
          variants={sectionVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
        >
          {/* CTA Card 1 */}
          <motion.div 
            variants={itemVariants}
            whileHover={{ y: -5, transition: { duration: 0.2 } }}
            className="col-span-1"
          >
            <div className="bg-white h-full rounded-2xl p-6 md:p-8 
                  border border-purple-200 hover:border-purple-400
                  transition-all duration-300
                  hover:shadow-[0_10px_40px_-15px_rgba(74,20,140,0.3)]
                  group relative overflow-hidden flex flex-col">
              
              {/* Background pattern */}
              <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-purple-100 to-transparent opacity-40 rounded-full -mr-10 -mt-10"></div>
              
              <div className="relative z-10 w-16 h-16 rounded-xl bg-gradient-to-br from-purple-900 to-purple-700
                  flex items-center justify-center mb-4 shadow-md group-hover:shadow-lg
                  group-hover:scale-110 transition-all duration-300">
                <RocketIcon size={28} strokeWidth={1.75} className="text-white" />
              </div>
              
              <h3 className="text-xl font-heading font-bold mb-3 text-purple-900">
                Join Developer Hub
              </h3>
              
              <p className="text-sm text-slate-600 leading-relaxed mb-4">
                Get exclusive access to resources, support, and early releases of new features.
              </p>
              
              <Link 
                href="/auth/signup"
                className="mt-auto py-3 px-6 rounded-lg text-white font-medium bg-gradient-to-r from-purple-900 to-purple-700
                  hover:shadow-lg hover:shadow-purple-200/40 transition-all duration-300 text-center"
              >
                Get Started
              </Link>
            </div>
          </motion.div>

          {/* CTA Card 2 - Featured/Large */}
          <motion.div 
            variants={itemVariants}
            whileHover={{ y: -5, transition: { duration: 0.2 } }}
            className="col-span-1 md:col-span-1"
          >
            <div className="bg-white h-full rounded-2xl p-6 md:p-8 
                  border border-purple-200 hover:border-purple-400
                  transition-all duration-300
                  hover:shadow-[0_15px_30px_-10px_rgba(123,31,162,0.25)]
                  group relative overflow-hidden flex flex-col">
              
              {/* Background pattern */}
              <div className="absolute top-0 right-0 w-full h-full">
                <div className="absolute top-0 right-0 w-40 h-40 bg-gradient-to-bl from-purple-100 to-transparent opacity-40 rounded-full -mr-10 -mt-10"></div>
              </div>
              
              <div className="relative z-10 w-16 h-16 rounded-xl bg-gradient-to-br from-purple-800 to-purple-600
                  flex items-center justify-center mb-4 shadow-md group-hover:shadow-lg
                  group-hover:scale-110 transition-all duration-300">
                <PlayCircleIcon size={28} strokeWidth={1.75} className="text-white" />
              </div>
              
              <h3 className="text-xl font-heading font-bold mb-3 text-purple-900">
                Watch Demo
              </h3>
              
              <p className="text-sm text-slate-600 leading-relaxed mb-4">
                See Crefy Connect in action with our comprehensive product demo.
              </p>
              
              <Link 
                href="#demo"
                className="mt-auto py-3 px-6 rounded-lg border-2 border-purple-700 text-purple-700 font-medium
                  hover:bg-purple-700 hover:text-white transition-all duration-300 text-center"
              >
                Play Demo
              </Link>
            </div>
          </motion.div>

          {/* CTA Card 3 */}
          <motion.div 
            variants={itemVariants}
            whileHover={{ y: -5, transition: { duration: 0.2 } }}
            className="col-span-1"
          >
            <div className="bg-white h-full rounded-2xl p-6 md:p-8 
                  border border-purple-200 hover:border-purple-400
                  transition-all duration-300
                  hover:shadow-[0_10px_40px_-15px_rgba(74,20,140,0.3)]
                  group relative overflow-hidden flex flex-col">
              
              {/* Background pattern */}
              <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-purple-100 to-transparent opacity-40 rounded-full -mr-10 -mt-10"></div>
              
              <div className="relative z-10 w-16 h-16 rounded-xl bg-gradient-to-br from-purple-700 to-purple-900
                  flex items-center justify-center mb-4 shadow-md group-hover:shadow-lg
                  group-hover:scale-110 transition-all duration-300">
                <CodeIcon size={28} strokeWidth={1.75} className="text-white" />
              </div>
              
              <h3 className="text-xl font-heading font-bold mb-3 text-purple-900">
                Documentation
              </h3>
              
              <p className="text-sm text-slate-600 leading-relaxed mb-4">
                Access comprehensive guides, API references and code samples.
              </p>
              
              <Link 
                href="/docs"
                className="mt-auto py-3 px-6 rounded-lg border-2 border-purple-700 text-purple-700 font-medium
                  hover:bg-purple-700 hover:text-white transition-all duration-300 text-center"
              >
                Read Docs
              </Link>
            </div>
          </motion.div>

        </motion.div>
      </div>
    </section>
  );
};

export default CTASection;