"use client"

import { memo, useCallback } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { X, Plus } from "lucide-react"
import { CreateApplicationRequest } from "@/lib/api"

interface ApplicationFormProps {
  formData: CreateApplicationRequest
  onFormDataChange: (data: CreateApplicationRequest) => void
  onSubmit: () => void
  onCancel: () => void
  isSubmitting?: boolean
  submitLabel?: string
}

export const ApplicationForm = memo(function ApplicationForm({
  formData,
  onFormDataChange,
  onSubmit,
  onCancel,
  isSubmitting = false,
  submitLabel = "Create Application"
}: ApplicationFormProps) {
  const updateField = useCallback((field: keyof CreateApplicationRequest, value: any) => {
    onFormDataChange({ ...formData, [field]: value })
  }, [formData, onFormDataChange])

  const addRedirectUrl = useCallback(() => {
    const newUrls = [...(formData.redirectUrls || []), ""]
    updateField("redirectUrls", newUrls)
  }, [formData.redirectUrls, updateField])

  const removeRedirectUrl = useCallback((index: number) => {
    const newUrls = formData.redirectUrls?.filter((_, i) => i !== index) || []
    updateField("redirectUrls", newUrls)
  }, [formData.redirectUrls, updateField])

  const updateRedirectUrl = useCallback((index: number, value: string) => {
    const newUrls = [...(formData.redirectUrls || [])]
    newUrls[index] = value
    updateField("redirectUrls", newUrls)
  }, [formData.redirectUrls, updateField])

  return (
    <div className="space-y-4">
      <div>
        <Label htmlFor="name" className="text-sm font-medium text-gray-700">
          Application Name *
        </Label>
        <Input
          id="name"
          value={formData.name}
          onChange={(e) => updateField("name", e.target.value)}
          placeholder="My Awesome App"
          className="mt-1"
          required
        />
      </div>

      <div>
        <Label htmlFor="description" className="text-sm font-medium text-gray-700">
          Description
        </Label>
        <Textarea
          id="description"
          value={formData.description || ""}
          onChange={(e) => updateField("description", e.target.value)}
          placeholder="Brief description of your application"
          className="mt-1"
          rows={3}
        />
      </div>

      <div>
        <Label htmlFor="iconUrl" className="text-sm font-medium text-gray-700">
          Icon URL
        </Label>
        <Input
          id="iconUrl"
          value={formData.iconUrl || ""}
          onChange={(e) => updateField("iconUrl", e.target.value)}
          placeholder="https://example.com/icon.png"
          className="mt-1"
          type="url"
        />
      </div>

      <div>
        <div className="flex items-center justify-between mb-2">
          <Label className="text-sm font-medium text-gray-700">
            Redirect URLs
          </Label>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={addRedirectUrl}
            className="h-8 px-3"
          >
            <Plus className="h-4 w-4 mr-1" />
            Add URL
          </Button>
        </div>
        <div className="space-y-2">
          {formData.redirectUrls?.map((url, index) => (
            <div key={index} className="flex items-center gap-2">
              <Input
                value={url}
                onChange={(e) => updateRedirectUrl(index, e.target.value)}
                placeholder="https://example.com/callback"
                className="flex-1"
                type="url"
              />
              <Button
                type="button"
                variant="outline"
                size="icon"
                onClick={() => removeRedirectUrl(index)}
                className="h-10 w-10 text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          )) || (
            <div className="text-sm text-gray-500 italic">
              No redirect URLs added yet. Click "Add URL" to add one.
            </div>
          )}
        </div>
      </div>

      <div className="flex justify-end gap-3 pt-4">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isSubmitting}
        >
          Cancel
        </Button>
        <Button
          type="button"
          onClick={onSubmit}
          disabled={!formData.name.trim() || isSubmitting}
          className="bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white hover:shadow-lg hover:shadow-[#4A148C]/25"
        >
          {isSubmitting ? "Creating..." : submitLabel}
        </Button>
      </div>
    </div>
  )
})
