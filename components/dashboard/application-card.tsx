"use client"

import { memo } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Copy, Trash2, Edit, MoreVertical, CheckCircle, AlertCircle } from "lucide-react"
import { ApplicationWithApiKey } from "@/lib/api"

interface ApplicationCardProps {
  app: ApplicationWithApiKey
  onEdit: (app: ApplicationWithApiKey) => void
  onDelete: (appId: string) => void
  onCopy: (text: string, label: string) => void
}

export const ApplicationCard = memo(function ApplicationCard({
  app,
  onEdit,
  onDelete,
  onCopy
}: ApplicationCardProps) {
  return (
    <Card className="bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg hover:shadow-2xl transform transition-all duration-300 hover:-translate-y-1 rounded-2xl">
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start">
          <div className="min-w-0 flex-1">
            <CardTitle className="text-lg sm:text-xl truncate bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent">
              {app.name}
            </CardTitle>
            <CardDescription className="text-xs sm:text-sm text-gray-600">
              Created on {new Date(app.createdAt).toLocaleDateString()}
            </CardDescription>
          </div>
          <div className="flex items-center gap-2 flex-shrink-0">
            <Badge variant={app.isActive ? "default" : "secondary"} className="text-xs">
              {app.isActive ? (
                <>
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Active
                </>
              ) : (
                <>
                  <AlertCircle className="h-3 w-3 mr-1" />
                  Inactive
                </>
              )}
            </Badge>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => onEdit(app)}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem 
                  className="text-red-600 hover:text-red-700"
                  onClick={() => onDelete(app.appId)}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-3 sm:space-y-4 pt-3">
        <div>
          <Label className="text-xs sm:text-sm font-medium text-gray-700">App ID</Label>
          <div className="flex items-center gap-2 mt-1">
            <code className="flex-1 p-2 bg-[#B497D6]/5 backdrop-blur-sm rounded-lg text-xs sm:text-sm font-mono border border-[#B497D6]/20 truncate text-[#4A148C]">
              {app.appId}
            </code>
            <Button
              variant="outline"
              size="icon"
              onClick={() => onCopy(app.appId, "App ID")}
              className="h-8 w-8 border-[#7B1FA2]/30 text-[#4A148C] hover:bg-[#7B1FA2]/10 hover:border-[#4A148C]"
            >
              <Copy className="h-3 w-3" />
            </Button>
          </div>
        </div>

        {app.description && (
          <div>
            <Label className="text-xs sm:text-sm font-medium text-gray-700">Description</Label>
            <p className="text-xs sm:text-sm text-gray-600 mt-1 p-2 bg-gray-50 rounded-lg border border-gray-200">
              {app.description}
            </p>
          </div>
        )}

        {app.redirectUrls && app.redirectUrls.length > 0 && (
          <div>
            <Label className="text-xs sm:text-sm font-medium text-gray-700">Redirect URLs</Label>
            <div className="mt-1 space-y-1">
              {app.redirectUrls.map((url, index) => (
                <div key={index} className="flex items-center gap-2">
                  <code className="flex-1 p-2 bg-[#B497D6]/5 backdrop-blur-sm rounded-lg text-xs sm:text-sm font-mono border border-[#B497D6]/20 truncate text-[#4A148C]">
                    {url}
                  </code>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => onCopy(url, "Redirect URL")}
                    className="h-8 w-8 border-[#7B1FA2]/30 text-[#4A148C] hover:bg-[#7B1FA2]/10 hover:border-[#4A148C]"
                  >
                    <Copy className="h-3 w-3" />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        )}

        {app.apiKey && (
          <div>
            <Label className="text-xs sm:text-sm font-medium text-gray-700">API Key</Label>
            <div className="flex items-center gap-2 mt-1">
              <code className="flex-1 p-2 bg-[#B497D6]/5 backdrop-blur-sm rounded-lg text-xs sm:text-sm font-mono border border-[#B497D6]/20 truncate text-[#4A148C]">
                {app.apiKey.substring(0, 20)}...
              </code>
              <Button
                variant="outline"
                size="icon"
                onClick={() => onCopy(app.apiKey, "API Key")}
                className="h-8 w-8 border-[#7B1FA2]/30 text-[#4A148C] hover:bg-[#7B1FA2]/10 hover:border-[#4A148C]"
              >
                <Copy className="h-3 w-3" />
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
})
