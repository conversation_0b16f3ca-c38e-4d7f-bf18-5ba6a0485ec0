'use client';

import Navbar from '@/components/landing/Navbar';
import HeroSection from '@/components/landing/HeroSection';
import DeveloperCodeSection from '@/components/landing/DeveloperCodeSection';
import TimelineSection from '@/components/landing/TimelineSection';
import ResourcesSection from '@/components/landing/ResourcesSection';
import CTASection from '@/components/landing/CTASection';
import Footer from '@/components/landing/Footer';
import Lightning from '@/components/ui/lightning';

export default function LandingPage() {
  return (
    <main className="min-h-screen text-brand-indigo font-body relative overflow-x-hidden flex flex-col">
      {/* Animated Lightning Background */}
      <div className="fixed inset-0 z-0">
        <Lightning
          hue={270}
          xOffset={0}
          speed={0.8}
          intensity={1.2}
          size={1.5}
        />
      </div>
      
      {/* Content positioned above the background */}
      <div className="flex-grow z-10 relative">
        <Navbar />
        <HeroSection />
        <DeveloperCodeSection />
        <TimelineSection />
        <ResourcesSection />
        <CTASection />
      </div>

      <Footer className="z-10 relative" />
    </main>
  );
}