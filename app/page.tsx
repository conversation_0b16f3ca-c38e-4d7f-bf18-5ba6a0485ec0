'use client';

import dynamic from 'next/dynamic';
import Navbar from '@/components/landing/Navbar';
import HeroSection from '@/components/landing/HeroSection';
import Lightning from '@/components/ui/lightning';

// Lazy load heavy components for better initial page load
const DeveloperCodeSection = dynamic(() => import('@/components/landing/DeveloperCodeSection'), {
  loading: () => <div className="h-96 animate-pulse bg-gray-100 rounded-lg" />
});

const TimelineSection = dynamic(() => import('@/components/landing/TimelineSection'), {
  loading: () => <div className="h-96 animate-pulse bg-gray-100 rounded-lg" />
});

const ResourcesSection = dynamic(() => import('@/components/landing/ResourcesSection'), {
  loading: () => <div className="h-96 animate-pulse bg-gray-100 rounded-lg" />
});

const CTASection = dynamic(() => import('@/components/landing/CTASection'), {
  loading: () => <div className="h-64 animate-pulse bg-gray-100 rounded-lg" />
});

const Footer = dynamic(() => import('@/components/landing/Footer'), {
  loading: () => <div className="h-32 animate-pulse bg-gray-100 rounded-lg" />
});

export default function RootPage() {
  return (
    <main className="min-h-screen text-brand-indigo font-body relative overflow-x-hidden flex flex-col">
      {/* Animated Lightning Background */}
      <div className="fixed inset-0 z-0">
        <Lightning
          hue={270}
          xOffset={0}
          speed={0.8}
          intensity={1.2}
          size={1.5}
        />
      </div>
      
      {/* Content positioned above the background */}
      <div className="flex-grow z-10 relative">
        <Navbar />
        <HeroSection />
        <DeveloperCodeSection />
        <TimelineSection />
        <ResourcesSection />
        <CTASection />
      </div>

      <Footer className="z-10 relative" />
    </main>
  );
}