"use client"
import { useRouter } from "next/navigation"
import { useAuth } from "@/lib/auth-context"
import { ToastProvider } from "@/lib/toast-context"
import { useEffect } from "react"

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { user, isLoading } = useAuth()
  const router = useRouter()
  
  useEffect(() => {
    // Only redirect if not loading and no user
    if (!isLoading && !user) {
      router.push('/auth/signin')
      return
    }
  }, [user, isLoading, router])
  
  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-white">
        <div className="text-center bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 rounded-2xl shadow-xl p-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#4A148C] mx-auto mb-4"></div>
          <p className="text-[#4A148C] font-medium">Loading dashboard...</p>
        </div>
      </div>
    )
  }
  
  // Don't render children if user is not authenticated
  if (!user) {
    return null
  }
  
  return (
      <ToastProvider>
        {children}
      </ToastProvider>
  )
}