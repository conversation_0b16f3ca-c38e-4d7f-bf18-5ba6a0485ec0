'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

import { useToast } from "@/lib/toast-context";
import { useAuth } from "@/lib/auth-context";
import { ConnectButton } from '@rainbow-me/rainbowkit';
import { useAccount } from 'wagmi';
import {
  WalletIcon,
  CheckCircleIcon,
  InfoIcon,
  GlobeIcon,
  LinkIcon,
  SearchIcon,
  ArrowLeftIcon,
  ArrowRightIcon
} from "lucide-react";


import { DashboardLayoutWrapper } from "@/components/shared/dashboard/layout-wrapper";
import dynamic from 'next/dynamic';

// Lazy load heavy ENS components for better performance
const ENSRootRegistration = dynamic(() => import("@/components/ens/ens-root-registration").then(mod => ({ default: mod.ENSRootRegistration })), {
  loading: () => <div className="animate-pulse bg-gray-200 rounded-lg h-32" />
});

const ApplicationSelection = dynamic(() => import("@/components/ens/application-selection").then(mod => ({ default: mod.ApplicationSelection })), {
  loading: () => <div className="animate-pulse bg-gray-200 rounded-lg h-20" />
});

const ENSNameEntry = dynamic(() => import("@/components/ens/ens-name-entry").then(mod => ({ default: mod.ENSNameEntry })), {
  loading: () => <div className="animate-pulse bg-gray-200 rounded-lg h-24" />
});

const ENSDashboard = dynamic(() => import("@/components/ens/ens-dashboard").then(mod => ({ default: mod.ENSDashboard })), {
  loading: () => <div className="animate-pulse bg-gray-200 rounded-lg h-40" />
});

const ENSTransferOwnership = dynamic(() => import("@/components/ens/ens-transfer-ownership").then(mod => ({ default: mod.ENSTransferOwnership })), {
  loading: () => <div className="animate-pulse bg-gray-200 rounded-lg h-32" />
});

import { ENSTransferFunctions } from "@/components/ens/ens-transfer-functions-simple";



import { StepProgress, Step } from "@/components/ens/step-progress";
import { BreadcrumbNavigation } from "@/components/ens/breadcrumb-navigation";
import { ContextualHelp } from "@/components/ens/contextual-help";
import { ENSConnection } from "@/lib/types/ens";
import { ErrorBoundary } from "@/components/shared/error-boundary";
import { ApplicationWithApiKey } from "@/lib/api";
import { ENSErrorHandler } from "@/lib/ens-error-handler";

type WorkflowStep = 'connect' | 'select-app' | 'enter-ens' | 'link-ens';

export default function ENSIntegrationPage() {
  const { address, isConnected } = useAccount();
  const { showToast } = useToast();
  const { token } = useAuth();

  const [selectedApplication, setSelectedApplication] = useState<ApplicationWithApiKey | null>(null);
  const [ensConnections, setEnsConnections] = useState<ENSConnection[]>([]);
  const [currentStep, setCurrentStep] = useState<WorkflowStep>('connect');
  const [completedSteps, setCompletedSteps] = useState<Set<WorkflowStep>>(new Set());
  const [enteredENSName, setEnteredENSName] = useState<string>('');
  const [isENSOwnershipVerified, setIsENSOwnershipVerified] = useState<boolean>(false);
  const [ensContractAddress, setEnsContractAddress] = useState<string>('');
  const [activeTab, setActiveTab] = useState<'integration' | 'transfer' | 'functions'>('integration');

  // Define workflow steps
  const getWorkflowSteps = (): Step[] => {
    const hasConnectedWallet = isConnected || completedSteps.has('connect');
    const hasSelectedApp = selectedApplication !== null || completedSteps.has('select-app');
    const hasEnteredValidENS = enteredENSName && isENSOwnershipVerified || completedSteps.has('enter-ens');
    const hasLinkedENS = ensConnections.some(conn => conn.projectId === selectedApplication?.appId) || completedSteps.has('link-ens');

    return [
      {
        id: 'connect',
        title: 'Connect Wallet',
        description: 'Connect your wallet using RainbowKit',
        status: hasConnectedWallet ? 'completed' : currentStep === 'connect' ? 'current' : 'upcoming',
        icon: <WalletIcon className="h-5 w-5" />
      },
      {
        id: 'select-app',
        title: 'Select Application',
        description: 'Choose which application to link ENS to',
        status: !hasConnectedWallet ? 'locked' :
                hasSelectedApp ? 'completed' :
                currentStep === 'select-app' ? 'current' : 'upcoming',
        icon: <GlobeIcon className="h-5 w-5" />
      },
      {
        id: 'enter-ens',
        title: 'Enter ENS Name',
        description: 'Enter your ENS name and verify ownership',
        status: !hasSelectedApp ? 'locked' :
                hasEnteredValidENS ? 'completed' :
                currentStep === 'enter-ens' ? 'current' : 'upcoming',
        icon: <SearchIcon className="h-5 w-5" />
      },
      {
        id: 'link-ens',
        title: 'Link ENS to Application',
        description: 'Register and link your ENS to the application',
        status: !hasEnteredValidENS ? 'locked' :
                hasLinkedENS ? 'completed' :
                currentStep === 'link-ens' ? 'current' : 'upcoming',
        icon: <LinkIcon className="h-5 w-5" />
      }
    ];
  };

  // Workflow navigation
  const navigateToStep = (stepId: string) => {
    const step = stepId as WorkflowStep;
    const steps = getWorkflowSteps();
    const targetStep = steps.find(s => s.id === stepId);
    
    if (targetStep && targetStep.status !== 'locked') {
      setCurrentStep(step);
    }
  };

  const goToNextStep = () => {
    const steps = getWorkflowSteps();
    const currentIndex = steps.findIndex(s => s.id === currentStep);
    const nextStep = steps[currentIndex + 1];
    
    if (nextStep && nextStep.status !== 'locked') {
      setCurrentStep(nextStep.id as WorkflowStep);
    }
  };

  const goToPreviousStep = () => {
    const steps = getWorkflowSteps();
    const currentIndex = steps.findIndex(s => s.id === currentStep);
    const previousStep = steps[currentIndex - 1];

    if (previousStep) {
      setCurrentStep(previousStep.id as WorkflowStep);

      // Reset state when going back to specific steps
      if (previousStep.id === 'connect') {
        // Reset all state when going back to connect step
        setSelectedApplication(null);
        setEnteredENSName('');
        setIsENSOwnershipVerified(false);
        setEnsContractAddress('');
        setCompletedSteps(new Set());

        // Show helpful message when going back to connect step
        showToast({
          type: 'info',
          title: 'Returned to Wallet Connection',
          description: 'Please reconnect your wallet to continue'
        });
      } else if (previousStep.id === 'select-app') {
        // Reset ENS-related state when going back to app selection
        setEnteredENSName('');
        setIsENSOwnershipVerified(false);
        setEnsContractAddress('');
        setCompletedSteps(prev => {
          const newSet = new Set(prev);
          newSet.delete('enter-ens');
          newSet.delete('link-ens');
          return newSet;
        });

        showToast({
          type: 'info',
          title: 'Returned to Application Selection',
          description: 'ENS verification has been reset. Please select an application to continue.'
        });
      } else if (previousStep.id === 'enter-ens') {
        // Reset linking state when going back to ENS entry
        setCompletedSteps(prev => {
          const newSet = new Set(prev);
          newSet.delete('link-ens');
          return newSet;
        });

        showToast({
          type: 'info',
          title: 'Returned to ENS Entry',
          description: 'You can modify your ENS name or verify a different one.'
        });
      }
    }
  };

  // Handle application selection
  const handleApplicationSelect = (_applicationId: string, application: ApplicationWithApiKey) => {
    setSelectedApplication(application);
    setCompletedSteps(prev => new Set([...prev, 'select-app']));

    // Auto-advance to ENS entry step
    if (currentStep === 'select-app') {
      setCurrentStep('enter-ens');
    }

    showToast({
      type: 'success',
      title: 'Application Selected',
      description: `Selected ${application.name} for ENS integration`
    });
  };

  // Handle ENS verification
  const handleENSVerified = (ensName: string, contractAddress: string) => {
    setEnteredENSName(ensName);
    setEnsContractAddress(contractAddress);
    setIsENSOwnershipVerified(true);
    setCompletedSteps(prev => new Set([...prev, 'enter-ens']));

    // Auto-advance to linking step
    if (currentStep === 'enter-ens') {
      setCurrentStep('link-ens');
    }

    showToast({
      type: 'success',
      title: 'ENS Ownership Verified',
      description: `${ensName} is ready to be linked to your application`
    });
  };

  // Auto-advance workflow based on state changes
  useEffect(() => {
    if (isConnected && currentStep === 'connect') {
      setCompletedSteps(prev => new Set([...prev, 'connect']));
      setCurrentStep('select-app');
    }
  }, [isConnected, currentStep]);

  // Handle hash navigation for quick transfer access
  useEffect(() => {
    const handleHashChange = () => {
      if (window.location.hash === '#transfer') {
        setActiveTab('transfer');
        window.location.hash = ''; // Clear the hash
      } else if (window.location.hash === '#functions') {
        setActiveTab('functions');
        window.location.hash = ''; // Clear the hash
      }
    };

    // Check initial hash
    handleHashChange();

    // Listen for hash changes
    window.addEventListener('hashchange', handleHashChange);

    return () => {
      window.removeEventListener('hashchange', handleHashChange);
    };
  }, []);

  // Handle wallet disconnection - allow user to stay on current step but reset connect step completion
  useEffect(() => {
    if (!isConnected) {
      setCompletedSteps(prev => {
        const newSet = new Set(prev);
        newSet.delete('connect');
        return newSet;
      });
    }
  }, [isConnected]);

  // Handle successful ENS root registration
  const handleENSRootSuccess = (data: any) => {
    showToast({
      type: 'success',
      title: 'ENS Linked Successfully! 🎉',
      description: `${enteredENSName} has been linked to ${selectedApplication?.name}`
    });

    // Add to connections
    const newConnection: ENSConnection = {
      id: Date.now().toString(),
      projectId: selectedApplication?.appId || '',
      appId: selectedApplication?.appId || '', // Add appId for better filtering
      ensName: enteredENSName,
      owner: address || '',
      connectedAt: new Date().toISOString(),
      isActive: data.isActive,
      contractAddress: address || '', // Use actual wallet address
      chain: 'sepolia'
    };

    setEnsConnections(prev => [...prev, newConnection]);
    setCompletedSteps(prev => new Set([...prev, 'link-ens']));

    // Show completion message
    setTimeout(() => {
      showToast({
        type: 'success',
        title: 'Integration Complete',
        description: 'Your ENS name is now linked to your application and ready for use. You can manage ownership through the dashboard below.'
      });
    }, 2000);
  };





  // Load existing ENS connections from API
  useEffect(() => {
    const loadENSConnections = async () => {
      if (!token || !selectedApplication) return;

      try {
        // TODO: Replace with actual API call to fetch ENS connections
        // const response = await apiService.getENSConnections(token, selectedApplication.appId);
        // if (response.success && response.data) {
        //   setEnsConnections(response.data);
        // }

        // For now, start with empty connections - they will be populated when users link ENS
        setEnsConnections([]);
      } catch (error) {
        console.error('Failed to load ENS connections:', error);
        setEnsConnections([]);
      }
    };

    loadENSConnections();
  }, [selectedApplication, token]);





  const steps = getWorkflowSteps();
  const currentStepIndex = steps.findIndex(s => s.id === currentStep);
  const canGoNext = currentStepIndex < steps.length - 1 && steps[currentStepIndex + 1]?.status !== 'locked';
  // Allow going back to any previous step, including Step 1 (connect wallet)
  const canGoPrevious = currentStepIndex > 0;

  const breadcrumbItems = [
    { id: 'dashboard', label: 'Dashboard' },
    { id: 'ens', label: 'ENS Integration', isActive: true },
    { id: currentStep, label: steps.find(s => s.id === currentStep)?.title || '', isActive: true }
  ];

  return (
    <ErrorBoundary>
      <DashboardLayoutWrapper title="ENS Integration">
        {/* Tab Navigation */}
        <div className="mb-6">
          <div className="flex items-center space-x-1 bg-gray-100 p-1 rounded-lg w-fit">
            <button
              onClick={() => setActiveTab('integration')}
              className={`px-4 py-2 text-sm font-medium rounded-md transition-all duration-200 ${
                activeTab === 'integration'
                  ? 'bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white shadow-md'
                  : 'text-gray-600 hover:text-[#4A148C] hover:bg-white/50'
              }`}
            >
              ENS Integration
            </button>
            <button
              onClick={() => setActiveTab('functions')}
              className={`px-4 py-2 text-sm font-medium rounded-md transition-all duration-200 ${
                activeTab === 'functions'
                  ? 'bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white shadow-md'
                  : 'text-gray-600 hover:text-[#4A148C] hover:bg-white/50'
              }`}
            >
              Transfer Functions
            </button>
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === 'integration' && (
          <div className="flex flex-col xl:flex-row gap-6 xl:gap-8">
          {/* Step Progress Sidebar */}
          <aside className="w-full xl:w-80 bg-white/90 backdrop-blur-sm border border-[#B497D6]/20 rounded-2xl shadow-lg p-4 lg:p-6 overflow-y-auto max-h-[calc(100vh-200px)] xl:sticky xl:top-0 order-2 xl:order-1">
            <div className="space-y-6">
              <div>
                <h2 className="text-lg sm:text-xl font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent mb-4 flex items-center gap-2">
                  <InfoIcon className="h-5 w-5 text-[#4A148C]" />
                  ENS Integration Guide
                </h2>
              </div>

              {/* Step Progress */}
              <StepProgress
                steps={steps}
                currentStepId={currentStep}
                onStepClick={navigateToStep}
              />

              {/* Contextual Help for Current Step */}
              {currentStep === 'connect' && (
                <ContextualHelp
                  title="Connect Your Wallet"
                  description="Connect your wallet to verify ENS ownership and interact with the Ethereum blockchain."
                  type="info"
                  benefits={['Secure verification', 'ENS ownership check', 'Blockchain interaction']}
                  requirements={['Compatible wallet (MetaMask, WalletConnect, etc.)', 'Ethereum network access']}
                  nextSteps={['Click Connect Wallet button', 'Approve connection in your wallet', 'Proceed to application selection']}
                />
              )}

              {currentStep === 'select-app' && (
                <ContextualHelp
                  title="Select Application"
                  description="Choose which application you want to register an ENS domain for."
                  type="info"
                  benefits={['Organized ENS management', 'Application-specific domains', 'Easy user identification']}
                  requirements={['At least one application created', 'Wallet connected']}
                  nextSteps={['Select your application from the list', 'Proceed to ENS domain registration']}
                  actions={[
                    {
                      label: 'Create Application',
                      onClick: () => window.location.href = '/dashboard/dashboard/applications',
                      variant: 'outline'
                    }
                  ]}
                />
              )}



              {/* Quick Stats */}
              <Card className="bg-gradient-to-br from-[#4A148C]/5 to-[#7B1FA2]/5 border-[#B497D6]/30 backdrop-blur-sm shadow-lg">
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm text-[#4A148C] flex items-center gap-2 font-semibold">
                    <GlobeIcon className="h-4 w-4" />
                    Your ENS Status
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0 space-y-3">
                  <div className="grid grid-cols-2 gap-3 text-xs">
                    <div className="text-center p-2 bg-white/60 rounded-lg">
                      <div className="font-semibold text-[#4A148C]">{selectedApplication ? 1 : 0}</div>
                      <div className="text-gray-600">Selected App</div>
                    </div>
                    <div className="text-center p-2 bg-white/60 rounded-lg">
                      <div className="font-semibold text-[#4A148C]">{ensConnections.length}</div>
                      <div className="text-gray-600">ENS Domains</div>
                    </div>
                  </div>
                  <div className="text-center p-2 bg-white/60 rounded-lg">
                    <div className="font-semibold text-green-600">{ensConnections.filter(c => c.isActive).length}</div>
                    <div className="text-gray-600 text-xs">Active Connections</div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </aside>

          {/* Main Content Area */}
          <main className="flex-1 overflow-y-auto order-1 xl:order-2">
            <div className="space-y-6">
              {/* Breadcrumb Navigation */}
              <BreadcrumbNavigation
                items={breadcrumbItems}
                onPrevious={canGoPrevious ? goToPreviousStep : undefined}
                onNext={canGoNext ? goToNextStep : undefined}
                previousLabel="Previous Step"
                nextLabel="Next Step"
                canGoPrevious={canGoPrevious}
                canGoNext={canGoNext}
              />

              {/* Step Content */}
              {currentStep === 'connect' && (
                <Card className="p-6 bg-gradient-to-br from-[#4A148C]/5 via-[#7B1FA2]/5 to-[#4A148C]/10 border border-[#B497D6]/30 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300">
                  <div className="text-center space-y-6">
                    <div className="w-16 h-16 mx-auto bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] rounded-full flex items-center justify-center shadow-lg">
                      <WalletIcon className="h-8 w-8 text-white" />
                    </div>

                    <div className="space-y-3">
                      <h3 className="text-2xl font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent">
                        Connect Your Wallet
                      </h3>
                      <p className="text-base text-gray-600 max-w-md mx-auto leading-relaxed">
                        To use ENS functionality, you need to connect your wallet. This allows us to verify
                        ENS ownership and register domains for your applications.
                      </p>
                    </div>

                    <div className="space-y-4">
                      <div className="flex justify-center">
                        <ConnectButton />
                      </div>

                      <div className="flex flex-col sm:flex-row items-center justify-center gap-4 text-sm text-gray-500">
                        <div className="flex items-center gap-2">
                          <CheckCircleIcon className="h-4 w-4 text-green-600" />
                          <span>Secure Connection</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <CheckCircleIcon className="h-4 w-4 text-green-600" />
                          <span>ENS Compatible</span>
                        </div>
                      </div>
                    </div>

                    <div className="pt-4 border-t border-[#B497D6]/20">
                      <p className="text-xs text-gray-500">
                        Supported wallets: MetaMask, WalletConnect, Coinbase Wallet, and more
                      </p>
                    </div>
                  </div>
                </Card>
              )}

              {currentStep === 'select-app' && (
                <ApplicationSelection
                  selectedApplicationId={selectedApplication?.appId}
                  onApplicationSelect={handleApplicationSelect}
                  onError={(error) => {
                    showToast({
                      type: 'error',
                      title: 'Application Error',
                      description: error
                    });
                  }}
                  className="mb-6"
                />
              )}

              {currentStep === 'enter-ens' && selectedApplication && (
                <ErrorBoundary>
                  <ENSNameEntry
                    onENSVerified={handleENSVerified}
                    onError={(error) => {
                      const ensError = ENSErrorHandler.handleError(new Error(error), 'ENS Name Entry');
                      ENSErrorHandler.showErrorToast(ensError);

                    }}
                  />
                </ErrorBoundary>
              )}

              {currentStep === 'link-ens' && selectedApplication && enteredENSName && isENSOwnershipVerified && (
                <ErrorBoundary>
                  <ENSRootRegistration
                    applicationId={selectedApplication.appId}
                    prefilledENSName={enteredENSName}
                    onSuccess={handleENSRootSuccess}
                    onError={(error) => {
                      const ensError = ENSErrorHandler.handleError(new Error(error), 'ENS Linking');
                      ENSErrorHandler.showErrorToast(ensError);
                      console.error('ENS Linking Error:', ensError);
                    }}
                  />
                </ErrorBoundary>
              )}



              {/* Navigation Buttons */}
              <div className="flex justify-between items-center mt-8 pt-6 border-t border-[#B497D6]/20">
                <Button
                  onClick={goToPreviousStep}
                  disabled={!canGoPrevious}
                  variant="outline"
                  className="border-[#7B1FA2]/30 text-[#4A148C] hover:bg-[#7B1FA2]/5 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <ArrowLeftIcon className="h-4 w-4 mr-2" />
                  Previous Step
                </Button>

                <div className="text-sm text-gray-600">
                  Step {currentStepIndex + 1} of {steps.length}
                </div>

                <Button
                  onClick={goToNextStep}
                  disabled={!canGoNext}
                  className="bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white hover:shadow-lg hover:shadow-[#4A148C]/25 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next Step
                  <ArrowRightIcon className="h-4 w-4 ml-2" />
                </Button>
              </div>

              {/* ENS Dashboard */}
              {selectedApplication && ensConnections.length > 0 && (
                <ENSDashboard
                  selectedApplication={selectedApplication}
                  ensConnections={ensConnections}
                  onRefresh={() => {
                    // Refresh ENS connections data
                    showToast({
                      type: 'info',
                      title: 'Refreshing...',
                      description: 'Updating ENS data'
                    });
                  }}
                  className="mt-8"
                />
              )}





            </div>
          </main>
        </div>
        )}

        {/* Transfer Ownership Tab */}
        {activeTab === 'transfer' && (
          <div className="max-w-4xl mx-auto">
            <ENSTransferOwnership
              ensName={enteredENSName || ensConnections[0]?.ensName || ''}
              currentOwner={address || ''}
            />
          </div>
        )}

        {/* Transfer Functions Tab */}
        {activeTab === 'functions' && (
          <div className="max-w-4xl mx-auto">
            <ENSTransferFunctions
              ensName={enteredENSName || ensConnections[0]?.ensName}
              currentOwner={address}
              appId={selectedApplication?.appId}
              onSuccess={(txHash, transferType) => {
                showToast({
                  type: 'success',
                  title: 'Transfer Function Complete',
                  description: `${transferType === 'registrar' ? 'Registrar creation' : 'NameWrapper transfer'} completed successfully`
                });
              }}
              onError={(error) => {
                showToast({
                  type: 'error',
                  title: 'Transfer Function Failed',
                  description: error
                });
              }}
            />
          </div>
        )}
      </DashboardLayoutWrapper>
    </ErrorBoundary>
  );
}
