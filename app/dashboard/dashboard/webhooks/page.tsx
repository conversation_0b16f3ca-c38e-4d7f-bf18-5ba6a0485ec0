"use client"

import React, { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Modal } from "@/components/shared/modal"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"

export default function WebhooksPage() {
  const [webhooks, setWebhooks] = useState([
    { 
      id: 1, 
      endpoint: "https://yourdomain.com/webhook1",  
      events: ["credential_issued", "wallet_connected"],
      active: true
    },
    { 
      id: 2, 
      endpoint: "https://yourdomain.com/webhook2",  
      events: ["user_registered"],
      active: false
    }
  ])
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [newWebhook, setNewWebhook] = useState({
    endpoint: "",
    events: [] as string[],
    active: true
  })

  const handleCreate = () => {
    if (!newWebhook.endpoint || newWebhook.events.length === 0) return
    
    const createdWebhook = {
      ...newWebhook,
      id: Date.now()
    }
    
    setWebhooks([...webhooks, createdWebhook])
    setNewWebhook({ endpoint: "", events: [], active: true })
    setIsModalOpen(false)
  }

  const handleToggle = (id: number) => {
    setWebhooks(webhooks.map(hook => 
      hook.id === id ? { ...hook, active: !hook.active } : hook
    ))
  }

  const handleDelete = (id: number) => {
    setWebhooks(webhooks.filter(hook => hook.id !== id))
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-bold">Webhooks</h2>
        <Button onClick={() => setIsModalOpen(true)}>Create Webhook</Button>
      </div>

      <div className="space-y-4">
        {webhooks.map((hook) => (
          <Card key={hook.id} className="p-6">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="font-semibold">{hook.endpoint}</h3>
                <div className="flex flex-wrap gap-2 mt-2">
                  {hook.events.map((event) => (
                    <Badge key={event} variant="outline">
                      {event.replace("_", " ")}
                    </Badge>
                  ))}
                </div>
              </div>
              <div className="flex gap-2">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => handleToggle(hook.id)}
                >
                  {hook.active ? "Disable" : "Enable"}
                </Button>
                <Button 
                  variant="destructive" 
                  size="sm"
                  onClick={() => handleDelete(hook.id)}
                >
                  Delete
                </Button>
              </div>
            </div>
          </Card>
        ))}
      </div>

      <Modal
        open={isModalOpen}
        onOpenChange={setIsModalOpen}
        title="Create Webhook"
        actionLabel="Create"
        onAction={handleCreate}
      >
        <div>
          <div className="space-y-2">
            <Label htmlFor="endpoint">Endpoint URL</Label>
            <input
              id="endpoint"
              type="text"
              value={newWebhook.endpoint}
              onChange={(e) => setNewWebhook({...newWebhook, endpoint: e.target.value})}
              className="w-full p-2 border rounded"
            />
          </div>
          
          <div className="space-y-2">
            <Label>Events to Subscribe</Label>
            <div className="grid grid-cols-2 gap-2">
              {["credential_issued", "wallet_connected", "user_registered", "api_call"].map((event) => (
                <div key={event} className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id={`event-${event}`}
                    checked={newWebhook.events.includes(event)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setNewWebhook({...newWebhook, events: [...newWebhook.events, event]})
                      } else {
                        setNewWebhook({...newWebhook, events: newWebhook.events.filter(e => e !== event)})
                      }
                    }}
                    className="h-4 w-4 rounded border-gray-300"
                  />
                  <Label htmlFor={`event-${event}`} className="text-sm">
                    {event.replace("_", " ")}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="active"
              checked={newWebhook.active}
              onChange={(e) => setNewWebhook({...newWebhook, active: e.target.checked})}
              className="h-4 w-4 rounded border-gray-300"
            />
            <Label htmlFor="active">Active</Label>
          </div>
        </div>
      </Modal>
    </div>
  )
}