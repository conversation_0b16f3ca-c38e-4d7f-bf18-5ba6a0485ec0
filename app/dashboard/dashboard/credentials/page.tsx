import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { CheckIcon, XIcon } from "lucide-react"

export default function CredentialsPage() {
  const credentials = [
    {
      id: 1,
      type: "KYC Verification",
      holder: "<PERSON> Do<PERSON>",
      issuer: "Crefy Identity",
      issued: "2023-01-15",
      expires: "2024-01-15",
      status: "active"
    },
    {
      id: 2,
      type: "ZK Identity",
      holder: "<PERSON>",
      issuer: "Crefy Identity",
      issued: "2023-02-10",
      expires: "2023-08-10",
      status: "expired"
    }
  ]

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-bold">Credentials</h2>
        <Button>Request New Credential</Button>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {credentials.map((cred) => (
          <Card key={cred.id} className="p-6">
            <h3 className="font-semibold">{cred.type}</h3>
            <div className="mt-4 space-y-2">
              <p className="text-sm text-muted-foreground">Holder: {cred.holder}</p>
              <p className="text-sm text-muted-foreground">Issuer: {cred.issuer}</p>
              <p className="text-sm text-muted-foreground">Expires: {cred.expires}</p>
              <Badge variant={cred.status === "active" ? "default" : "destructive"}>
                {cred.status === "active" ? (
                  <>
                    <CheckIcon className="mr-1 h-3 w-3" />
                    Active
                  </>
                ) : (
                  <>
                    <XIcon className="mr-1 h-3 w-3" />
                    Expired
                  </>
                )}
              </Badge>
            </div>
          </Card>
        ))}
      </div>
    </div>
  )
}