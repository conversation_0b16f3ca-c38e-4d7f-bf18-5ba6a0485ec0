"use client"

import React, { useEffect } from "react"
import { useRouter } from "next/navigation"

export default function DashboardPage() {
  const router = useRouter()

  useEffect(() => {
    // Redirect to applications page as the main dashboard
    router.replace('/dashboard/dashboard/applications')
  }, [router])

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#4A148C] mx-auto mb-4"></div>
        <p className="text-gray-600">Redirecting to Applications...</p>
      </div>
    </div>
  )
}