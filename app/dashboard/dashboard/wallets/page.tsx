"use client"

import React, { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { WalletConnect, useWalletConnection } from "@/components/shared/client-wallet-connect"
import { useDisconnect } from 'wagmi'

export default function WalletsPage() {
  const { isConnected, address } = useWalletConnection()
  const { disconnect } = useDisconnect()
  const [connectedWallets, setConnectedWallets] = useState([
    { id: 1, name: "Coinbase Wallet", address: "0x1234...5678", provider: "coinbase" }
  ])

  const handleConnect = (walletAddress: string) => {
    // Add the newly connected wallet to the list
    const newWallet = {
      id: Date.now(),
      name: "Coinbase Wallet",
      address: walletAddress,
      provider: "coinbase"
    }
    
    setConnectedWallets([...connectedWallets, newWallet])
  }

  const handleDisconnect = async (id: number) => {
    setConnectedWallets(connectedWallets.filter(wallet => wallet.id !== id))
    if (isConnected) {
      await disconnect()
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-bold">Coinbase Wallet</h2>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* Show connected wallet from provider */}
        {isConnected && address && (
          <Card className="p-6">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="font-semibold">Coinbase Wallet</h3>
                <p className="text-sm text-muted-foreground mt-2">
                  {address.slice(0, 6)}...{address.slice(-4)}
                </p>
                <div className="mt-2">
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
                    Connected
                  </span>
                </div>
              </div>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => disconnect()}
              >
                Disconnect
              </Button>
            </div>
          </Card>
        )}

        {/* Show other connected wallets from state */}
        {connectedWallets.map((wallet) => (
          <Card key={wallet.id} className="p-6">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="font-semibold">{wallet.name}</h3>
                <p className="text-sm text-muted-foreground mt-2">{wallet.address}</p>
                <div className="mt-2">
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-800">
                    Saved
                  </span>
                </div>
              </div>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => handleDisconnect(wallet.id)}
              >
                Remove
              </Button>
            </div>
          </Card>
        ))}
      </div>

      {/* Coinbase Wallet Connect Component */}
      <div className="flex justify-center">
        <WalletConnect
          onConnect={handleConnect}
          onDisconnect={() => {}}
          className="w-full max-w-md"
        />
      </div>
    </div>
  )
}