import React from "react"
import { Card } from "@/components/ui/card"
import { CardContent } from "@/components/ui/card-content"
import { But<PERSON> } from "@/components/ui/button"
import { DashboardSidebar } from "@/components/shared/dashboard/sidebar"

export default function AdminPage() {
  return (
    <div className="flex min-h-screen">
      <DashboardSidebar />
      <DashboardSidebar />
      
      <main className="flex-1 p-6 lg:p-8">
        <header className="flex items-center justify-between mb-8">
          <h1 className="text-2xl font-bold">Admin Dashboard</h1>
          <div className="flex items-center gap-4">
            <Button variant="outline" size="sm">New User</Button>
            <div className="h-8 w-8 rounded-full bg-muted flex items-center justify-center">
              <span className="text-sm">AD</span>
            </div>
          </div>
        </header>

        {/* Analytics Overview */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-8">
          {[{ label: "Total Users", value: "1,234" }, { label: "Active Projects", value: "89" }, { label: "API Requests", value: "234k" }, { label: "Verification Rate", value: "98.7%" }].map((item, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <p className="text-sm text-muted-foreground">{item.label}</p>
                <h3 className="text-2xl font-bold mt-2">{item.value}</h3>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Users Table */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <h2 className="text-lg font-semibold mb-4">User Management</h2>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4">Name</th>
                    <th className="text-left py-3 px-4">Role</th>
                    <th className="text-left py-3 px-4">Status</th>
                    <th className="text-right py-3 px-4">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {[1, 2, 3].map((i) => (
                    <tr key={i} className="border-b">
                      <td className="py-3 px-4">John Doe</td>
                      <td className="py-3 px-4">Developer</td>
                      <td className="py-3 px-4">
                        <span className="inline-block w-2 h-2 rounded-full bg-green-500 mr-2"></span>
                        Active
                      </td>
                      <td className="py-3 px-4 text-right">
                        <Button variant="outline" size="sm" className="mr-2">View</Button>
                        <Button variant="destructive" size="sm">Ban</Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Credential Schema Manager */}
        <Card>
          <CardContent className="p-6">
            <h2 className="text-lg font-semibold mb-4">Credential Schema</h2>
            <div className="grid gap-4 md:grid-cols-2">
              <div className="border rounded-lg p-4">
                <h3 className="font-medium mb-2">KYC Verification</h3>
                <p className="text-sm text-muted-foreground mb-4">Expiration: 1 year</p>
                <Button variant="outline" size="sm">Edit</Button>
              </div>
              <div className="border rounded-lg p-4">
                <h3 className="font-medium mb-2">ZK Identity</h3>
                <p className="text-sm text-muted-foreground mb-4">Expiration: 6 months</p>
                <Button variant="outline" size="sm">Edit</Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  )
}