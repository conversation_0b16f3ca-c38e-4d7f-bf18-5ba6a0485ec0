"use client"

import React, { useState } from "react"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Modal } from "@/components/shared/modal"
import { Input } from "@/components/ui/input"

export default function ENSSchemasPage() {
  const [schemas, setSchemas] = useState([
    { id: 1, name: "ENS Domain Verification", enabled: true, expiration: "1 year" },
    { id: 2, name: "ENS Avatar Integration", enabled: true, expiration: "6 months" },
    { id: 3, name: "ENS Subdomain Management", enabled: false, expiration: "5 years" }
  ])
  const [editingSchema, setEditingSchema] = useState<typeof schemas[0] | null>(null)

  const handleToggleSchema = (id: number, enabled: boolean) => {
    setSchemas(schemas.map(schema => 
      schema.id === id ? { ...schema, enabled } : schema
    ))
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-bold">ENS Integration Schemas</h2>
        <Button onClick={() => setEditingSchema({ id: 0, name: "", enabled: true, expiration: "1 year" })}>
          New Schema
        </Button>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {schemas.map((schema) => (
          <Card key={schema.id} className="p-6">
            <div className="flex justify-between items-start">
              <h3 className="font-semibold">{schema.name}</h3>
              <Switch
                checked={schema.enabled}
                onCheckedChange={(checked) => handleToggleSchema(schema.id, checked)}
              />
            </div>
            <div className="mt-4 space-y-2">
              <p className="text-sm text-muted-foreground">
                Expiration: {schema.expiration}
              </p>
              <Button variant="outline" size="sm" onClick={() => setEditingSchema(schema)}>
                Edit Configuration
              </Button>
            </div>
          </Card>
        ))}
      </div>

      {editingSchema && (
        <Modal
          open={!!editingSchema}
          onOpenChange={() => setEditingSchema(null)}
          title={editingSchema.id === 0 ? "Create New Schema" : "Edit Schema"}
          actionLabel="Save Changes"
          onAction={() => {
            // Handle schema action
          }}
        >
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="schema-name">Schema Name</Label>
              <Input
                id="schema-name"
                value={editingSchema.name}
                onChange={(e) => setEditingSchema({...editingSchema, name: e.target.value})}
                placeholder="Enter schema name"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="expiration">Expiration Period</Label>
              <select
                id="expiration"
                value={editingSchema.expiration}
                onChange={(e) => setEditingSchema({...editingSchema, expiration: e.target.value})}
                className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
              >
                <option value="6 months">6 months</option>
                <option value="1 year">1 year</option>
                <option value="3 years">3 years</option>
                <option value="5 years">5 years</option>
              </select>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="enabled"
                checked={editingSchema.enabled}
                onCheckedChange={(checked) => setEditingSchema({...editingSchema, enabled: checked})}
              />
              <Label htmlFor="enabled">Enable Schema</Label>
            </div>
          </div>
        </Modal>
      )}
    </div>
  )
}