"use client"

import React, { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Logs<PERSON>iewer } from "@/components/shared/logs-viewer"

export default function UsersPage() {
  const [users, setUsers] = useState([
    { id: 1, name: "<PERSON>", email: "<EMAIL>", role: "developer", status: "active", lastLogin: "2023-03-20" },
    { id: 2, name: "<PERSON>", email: "<EMAIL>", role: "developer", status: "active", lastLogin: "2023-03-19" },
    { id: 3, name: "Admin User", email: "<EMAIL>", role: "admin", status: "active", lastLogin: "2023-03-21" },
    { id: 4, name: "Inactive User", email: "<EMAIL>", role: "developer", status: "inactive", lastLogin: null },
  ])
  const [search, setSearch] = useState("")

  const handleBan = (id: number) => {
    setUsers(users.map(user => 
      user.id === id ? { ...user, status: user.status === "active" ? "banned" : "active" } : user
    ))
  }

  const handleResetPassword = (id: number) => {
    alert(`Password reset for user ${id}`)
  }

  const filteredUsers = users.filter(user => 
    user.name.toLowerCase().includes(search.toLowerCase()) ||
    user.email.toLowerCase().includes(search.toLowerCase())
  )

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-bold">User Management</h2>
        <div className="flex gap-3">
          <Input
            placeholder="Search users..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="max-w-sm"
          />
          <Button>New User</Button>
        </div>
      </div>

      <Card>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-muted/50">
              <tr>
                <th className="text-left p-4">Name</th>
                <th className="text-left p-4">Email</th>
                <th className="text-left p-4">Role</th>
                <th className="text-left p-4">Status</th>
                <th className="text-left p-4">Last Login</th>
                <th className="text-right p-4">Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredUsers.map((user) => (
                <tr key={user.id} className="border-b hover:bg-muted/50">
                  <td className="p-4">{user.name}</td>
                  <td className="p-4">{user.email}</td>
                  <td className="p-4">
                    <Badge variant={user.role === "admin" ? "default" : "outline"}>
                      {user.role}
                    </Badge>
                  </td>
                  <td className="p-4">
                    <Badge 
                      variant={user.status === "active" ? "default" : 
                              user.status === "banned" ? "destructive" : "secondary"}
                    >
                      {user.status}
                    </Badge>
                  </td>
                  <td className="p-4">{user.lastLogin || "Never"}</td>
                  <td className="p-4 text-right">
                    <Button variant="outline" size="sm" className="mr-2" onClick={() => handleResetPassword(user.id)}>
                      Reset Password
                    </Button>
                    <Button 
                      variant={user.status === "active" ? "destructive" : "default"} 
                      size="sm" 
                      onClick={() => handleBan(user.id)}
                    >
                      {user.status === "active" ? "Ban" : "Unban"}
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>

      <div className="grid gap-6 md:grid-cols-2">
        <Card className="p-6">
          <h3 className="font-semibold mb-4">User Activity</h3>
          <LogsViewer logs={[
            { id: 1, timestamp: "2023-03-21 14:23", level: "info", message: "User logged in" },
            { id: 2, timestamp: "2023-03-21 14:25", level: "info", message: "Wallet connected" },
            { id: 3, timestamp: "2023-03-21 14:30", level: "warning", message: "Failed API call" },
          ]} />
        </Card>

        <Card className="p-6">
          <h3 className="font-semibold mb-4">Project Usage</h3>
          <div className="space-y-4">
            {["Web3 Wallet", "Decentralized ID", "NFT Marketplace"].map((project, i) => (
              <div key={i} className="flex justify-between">
                <span>{project}</span>
                <span className="font-semibold">{100 - i * 15}k</span>
              </div>
            ))}
          </div>
        </Card>
      </div>
    </div>
  )
}