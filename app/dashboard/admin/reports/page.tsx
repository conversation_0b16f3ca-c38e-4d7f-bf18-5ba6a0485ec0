"use client"

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts'

export default function ReportsPage() {
  const usageData = [
    { month: "Jan", ensConnections: 120, verifications: 80 },
    { month: "Feb", ensConnections: 150, verifications: 95 },
    { month: "Mar", ensConnections: 170, verifications: 110 },
    { month: "Apr", ensConnections: 140, verifications: 90 },
    { month: "May", ensConnections: 180, verifications: 120 },
  ]

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-bold">Analytics & Reports</h2>

      <div className="h-[400px]">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={usageData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Bar dataKey="ensConnections" fill="#B497D6" />
            <Bar dataKey="verifications" fill="#4B0082" />
          </BarChart>
        </ResponsiveContainer>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <div className="bg-card rounded-lg p-6 shadow">
          <h3 className="text-lg font-semibold mb-4">Top Projects by Usage</h3>
          <div className="space-y-4">
            {["Web3 Wallet", "Decentralized ID", "NFT Marketplace"].map((project, i) => (
              <div key={i} className="flex justify-between">
                <span>{project}</span>
                <span className="font-semibold">{100 - i * 15}k</span>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-card rounded-lg p-6 shadow">
          <h3 className="text-lg font-semibold mb-4">Verification Success Rate</h3>
          <div className="text-3xl font-bold text-center mt-4">98.7%</div>
          <p className="text-sm text-muted-foreground text-center mt-2">Compared to 95.2% last month</p>
        </div>
      </div>
    </div>
  )
}