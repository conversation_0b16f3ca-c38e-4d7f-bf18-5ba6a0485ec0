"use client";

import { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useAuth } from "@/lib/auth-context";
import { apiService } from "@/lib/api";

export default function SignupPage() {
  const [step, setStep] = useState(1);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    password: "",
  });
  const [otp, setOtp] = useState("");
  const [status, setStatus] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [cooldown, setCooldown] = useState(false);
  const router = useRouter();
  const {} = useAuth();

  const handleResendOTP = async () => {
    if (cooldown || isLoading) return;

    setIsLoading(true);
    setStatus("Resending OTP...");
    setCooldown(true);

    try {
      const response = await apiService.register({
        name: formData.name,
        email: formData.email,
        password: formData.password,
      });

      if (response.success) {
        setStatus("OTP sent again!");
      } else {
        setStatus(response.error || "Failed to resend OTP");
      }
    } catch {
      setStatus("Failed to resend OTP");
    } finally {
      setIsLoading(false);
      setTimeout(() => {
        setCooldown(false);
      }, 30000);
    }
  };

  const handleSignup = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);

    if (step === 1) {
      // Register user
      if (formData.email && formData.password) {
        setStatus("Sending OTP...");

        try {
          const response = await apiService.register({
            name: formData.name,
            email: formData.email,
            password: formData.password,
          });

          if (response.success) {
            setStatus("OTP sent to your email!");
            setStep(2);
          } else {
            setStatus(response.error || "Registration failed");
          }
        } catch {
          setStatus("Registration failed. Please try again.");
        }
      }
    } else {
      // Verify OTP
      if (otp) {
        setStatus("Verifying...");

        try {
          const response = await apiService.verify({
            email: formData.email,
            otp: otp,
          });

          if (response.success) {
            setStatus("Verified! Please sign in with your credentials.");

            // Redirect to signin page after successful verification
            setTimeout(() => {
              router.push("/auth/signin");
            }, 2000);
          } else {
            setStatus(response.error || "Invalid OTP");
          }
        } catch {
          setStatus("Verification failed. Please try again.");
        }
      } else {
        setStatus("Please enter the OTP");
      }
    }
    
    setIsLoading(false);
  };

  return (
    <main className="min-h-screen flex items-center justify-center bg-white relative overflow-hidden px-4">
      {/* Animated Background Elements */}
      <div className="absolute w-64 h-64 bg-[#7B1FA2]/10 rounded-full blur-3xl animate-pulse"></div>
      <div className="absolute w-80 h-80 bg-[#4A148C]/5 rounded-full blur-3xl animate-float" style={{ top: "20%", left: "10%" }}></div>
      <div className="absolute w-48 h-48 bg-[#7B1FA2]/5 rounded-full blur-3xl animate-bounce-slow" style={{ bottom: "20%", right: "10%" }}></div>

      {/* Sign Up Card */}
      <div className="relative z-10 w-full max-w-md p-8 bg-white/80 backdrop-blur-sm border border-gray-200 rounded-2xl shadow-xl space-y-6 transition-all duration-300 hover:shadow-2xl">
        <div className="text-center">
          <h1 className="text-2xl font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent">
            Create Developer Account
          </h1>
          <p className="text-gray-600 mt-2">Start integrating Web3 features today</p>
        </div>

        {/* Step Form */}
        {step === 1 ? (
          <form onSubmit={handleSignup} className="space-y-5">
            <div className="space-y-2">
              <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                Full Name
              </label>
              <input
                id="name"
                type="text"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                required
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#7B1FA2] transition-shadow duration-200"
                placeholder="John Doe"
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email
              </label>
              <input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                required
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#7B1FA2] transition-shadow duration-200"
                placeholder="<EMAIL>"
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                Password
              </label>
              <input
                id="password"
                type="password"
                value={formData.password}
                onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                required
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#7B1FA2] transition-shadow duration-200"
                placeholder="••••••••"
              />
            </div>

            {status && (
              <p className={`text-sm ${status.includes("failed") || status.includes("Failed") ? "text-red-500" : "text-gray-600"} text-center`}>
                {status}
              </p>
            )}              <button
              type="submit"
              disabled={isLoading}
              className="w-full py-3 bg-[#4A148C] text-white rounded-lg hover:bg-opacity-90 transition-all duration-300 shadow-md hover:shadow-lg font-medium disabled:opacity-50"
            >
              {isLoading ? "Sending..." : "Continue"}
            </button>
          </form>
        ) : (
          <form onSubmit={handleSignup} className="space-y-5">
            <div className="space-y-2">
              <label htmlFor="otp" className="block text-sm font-medium text-gray-700">
                Verification Code
              </label>
              <input
                id="otp"
                maxLength={6}
                value={otp}
                onChange={(e) => setOtp(e.target.value)}
                required
                className="w-full px-4 py-2 text-center text-2xl tracking-widest border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#7B1FA2] transition-shadow duration-200"
              />
              <p className="text-sm text-gray-500 text-center">
                Enter the 6-digit code sent to {formData.email}
              </p>
            </div>

            {status && (
              <p className={`text-sm ${status.includes("failed") || status.includes("Failed") || status.includes("Invalid") ? "text-red-500" : "text-gray-600"} text-center`}>
                {status}
              </p>
            )}

            <button
              type="submit"
              disabled={isLoading}
              className="w-full py-3 bg-[#4A148C] text-white rounded-lg hover:bg-opacity-90 transition-all duration-300 shadow-md hover:shadow-lg font-medium disabled:opacity-50"
            >
              {isLoading ? "Verifying..." : "Verify"}
            </button>

            <p className="text-sm text-center text-gray-600">
              Didn&apos;t receive code?{" "}
              <button
                type="button"
                onClick={handleResendOTP}
                disabled={cooldown || isLoading}
                className={`text-[#4A148C] hover:underline disabled:opacity-50`}
              >
                {isLoading ? "Sending..." : `Resend ${cooldown ? "(30s)" : ""}`}
              </button>
            </p>
          </form>
        )}

        <div className="text-center space-y-2">
          <p className="text-sm text-gray-600">
            Already have an account?{" "}
            <Link href="/auth/signin" className="text-[#4A148C] hover:underline font-medium">
              Sign in
            </Link>
          </p>
        </div>
      </div>
    </main>
  );
}