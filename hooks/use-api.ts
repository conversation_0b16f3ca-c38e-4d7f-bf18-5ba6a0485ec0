import { useAuth } from "@/lib/auth-context";
import { apiService } from "@/lib/api";

export function useApi() {
  const { token, logout } = useAuth();

  const makeAuthenticatedRequest = async <T>(
    requestFn: (token: string) => Promise<{ success: boolean; data?: T; error?: string }>
  ): Promise<{ success: boolean; data?: T; error?: string }> => {
    if (!token) {
      return {
        success: false,
        error: "No authentication token available",
      };
    }

    const response = await requestFn(token);

    // If we get an unauthorized response, logout the user
    if (!response.success && response.error?.includes("unauthorized")) {
      logout();
      return {
        success: false,
        error: "Session expired. Please login again.",
      };
    }

    return response;
  };

  return {
    makeAuthenticatedRequest,
    token,
  };
}