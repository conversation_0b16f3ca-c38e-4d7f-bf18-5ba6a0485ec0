import { useState, useEffect, useCallback, useMemo } from 'react'
import { apiService, ApplicationWithApiKey } from '@/lib/api'
import { toast } from 'sonner'

interface UseApplicationsReturn {
  applications: ApplicationWith<PERSON>piKey[]
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
  deleteApplication: (appId: string) => Promise<void>
}

export function useApplications(token: string | null): UseApplicationsReturn {
  const [applications, setApplications] = useState<ApplicationWithApiKey[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchApplications = useCallback(async () => {
    if (!token) {
      setApplications([])
      return
    }

    try {
      setLoading(true)
      setError(null)
      const response = await apiService.getApplications(token)
      
      if (response.success && response.data) {
        const apps = Array.isArray(response.data) ? response.data : []
        setApplications(apps)
      } else {
        throw new Error('Failed to fetch applications')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch applications'
      setError(errorMessage)
      toast.error('Failed to load applications', {
        description: errorMessage
      })
    } finally {
      setLoading(false)
    }
  }, [token])

  const deleteApplication = useCallback(async (appId: string) => {
    if (!token) return

    try {
      const response = await apiService.deleteApplication(appId, token)
      if (response.success) {
        setApplications(prev => prev.filter(app => app.appId !== appId))
        toast.success('Application deleted successfully')
      } else {
        throw new Error('Failed to delete application')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete application'
      toast.error('Failed to delete application', {
        description: errorMessage
      })
      throw err
    }
  }, [token])

  useEffect(() => {
    fetchApplications()
  }, [fetchApplications])

  const memoizedReturn = useMemo(() => ({
    applications,
    loading,
    error,
    refetch: fetchApplications,
    deleteApplication
  }), [applications, loading, error, fetchApplications, deleteApplication])

  return memoizedReturn
}
