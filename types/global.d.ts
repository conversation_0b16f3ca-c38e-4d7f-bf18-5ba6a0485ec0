// Global type declarations for blockchain-related packages

declare module '*.svg' {
  const content: any;
  export default content;
}

declare module '*.png' {
  const content: any;
  export default content;
}

declare module '*.jpg' {
  const content: any;
  export default content;
}

declare module '*.jpeg' {
  const content: any;
  export default content;
}

declare module '*.gif' {
  const content: any;
  export default content;
}

declare module '*.webp' {
  const content: any;
  export default content;
}

// Extend Window interface for Web3 providers
declare global {
  interface Window {
    ethereum?: any;
    web3?: any;
  }
}

// Type augmentations for blockchain packages
declare module 'viem' {
  interface Config {
    [key: string]: any;
  }
}

declare module '@wagmi/core' {
  interface Config {
    [key: string]: any;
  }
}

declare module 'wagmi' {
  interface Config {
    [key: string]: any;
  }
}

// Abitype module declarations
declare module 'abitype' {
  export type Abi = any[];
  export type AbiFunction = any;
  export type AbiEvent = any;
  export type AbiError = any;
  export type AbiParameter = any;
  export type AbiParameterToPrimitiveType<T> = any;
  export type ExtractAbiFunction<T, N> = any;
  export type ExtractAbiFunctionNames<T> = any;
  export type ExtractAbiEvent<T, N> = any;
  export type ExtractAbiEventNames<T> = any;
  export type ExtractAbiError<T, N> = any;
  export type ExtractAbiErrorNames<T> = any;
}

// Ox module declarations
declare module 'ox' {
  export namespace Abi {
    export type Abi = any[];
    export type Function = any;
    export type Event = any;
    export type Error = any;
    export type Parameter = any;
  }
  
  export namespace Block {
    export type Block = any;
  }
  
  export namespace Transaction {
    export type Transaction = any;
    export type TransactionEnvelope = any;
    export type TransactionReceipt = any;
  }
  
  export namespace TypedData {
    export type TypedData = any;
    export type Domain = any;
    export type Types = any;
  }
  
  export namespace Signature {
    export type Signature = any;
  }
  
  export namespace Authorization {
    export type Authorization = any;
  }
  
  export namespace Blobs {
    export type Blob = any;
    export type BlobSidecar = any;
  }
}

export {};