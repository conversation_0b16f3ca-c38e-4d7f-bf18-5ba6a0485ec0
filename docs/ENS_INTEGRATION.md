# ENS Integration Guide - Crefy Connect Platform

## Overview

This document provides a comprehensive guide to the Ethereum Name Service (ENS) integration in the Crefy Connect platform. Our implementation allows developers to register ENS root domains, check subname availability, and claim subnames through a seamless API and smart contract integration.

## What is ENS?

The Ethereum Name Service (ENS) is a distributed, open, and extensible naming system based on the Ethereum blockchain. ENS maps human-readable names like `alice.eth` to machine-readable identifiers such as Ethereum addresses, other cryptocurrency addresses, content hashes, and metadata.

### Key Benefits:
- **Human-readable addresses**: Replace complex wallet addresses with memorable names
- **Decentralized ownership**: True ownership of your digital identity
- **Cross-platform compatibility**: Works across all Ethereum-compatible applications
- **Extensible**: Support for multiple record types (addresses, content, text records)

## Architecture Overview

Our ENS integration consists of three main components:

### 1. Frontend Components (React/Next.js)
- **ENS Root Registration**: Register `.eth` domains for your applications
- **Subname Availability Checker**: Check if subnames are available
- **Subname Claiming**: Allow users to claim subnames under your root domain
- **Wallet Integration**: RainbowKit for seamless wallet connection

### 2. Backend API (Crefy Connect API)
- **ENS Root Management**: `/ens/roots` - Register and manage root domains
- **Subname Availability**: `/ens/subnames/check-availability` - Check availability
- **Subname Claiming**: `/ens/subnames/claim` - Claim subnames with gas abstraction

### 3. Smart Contracts (Ethereum/Sepolia)
- **Factory Contract**: Creates subname registrar contracts
- **L1 Subname Registrar**: Handles subname claiming and management
- **Name Wrapper**: ENS v2 wrapper for enhanced functionality

## API Endpoints

### Register ENS Root Domain
```http
POST /ens/roots
Authorization: Bearer <token>
Content-Type: application/json

{
  "ens_name": "myplatform.eth",
  "contractAddress": "******************************************",
  "chain": "sepolia",
  "isActive": true
}
```

**Response:**
```json
{
  "success": true,
  "message": "ENS root registered successfully",
  "data": {
    "name": "myplatform.eth",
    "contractAddress": "******************************************",
    "isActive": true,
    "ownerAppId": "app_123456789"
  }
}
```

### Check Subname Availability
```http
GET /ens/subnames/check-availability?subName=user123&chain=sepolia
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "message": "Subname is available",
  "available": true
}
```

### Claim ENS Subname
```http
POST /ens/subnames/claim
Authorization: Bearer <token>
Content-Type: application/json

{
  "subName": "user123",
  "chain": "sepolia",
  "useAccountAbstraction": true
}
```

**Response:**
```json
{
  "success": true,
  "message": "Subname claimed successfully",
  "data": {
    "status": "success",
    "address": "******************************************",
    "hash": "0x123...abc",
    "gasUsed": "21000",
    "effectiveGasPrice": "**********",
    "estimatedGas": "21000",
    "estimatedCost": "2**********000",
    "chainId": ********
  }
}
```

## Smart Contract Integration

### Contract Addresses (Sepolia Testnet)
- **Factory Contract**: `0x0dBA7bd3240c86090Cd53bE0D9DaB99b466A36D3`
- **Name Wrapper**: `0x0635513f179D50A207757E05759CbD106d7dFcE8`
- **ENS Registry**: `0x00000000000C2E074eC69A0dFb2997BA6C7d2e1e`

### Key Functions

#### Factory Contract
- `createSubnameRegistrar(bytes32 _parentNode)`: Creates a new subname registrar
- `getSubnameContractsByOwner(address owner)`: Get contracts owned by address

#### L1 Subname Registrar
- `claimName(string label)`: Claim a subname under the parent domain
- `isNameClaimed(string label)`: Check if a name is already claimed
- `viewAllSubnames()`: Get all registered subnames

## OnchainKit Reference

Our implementation draws inspiration from Coinbase's OnchainKit, particularly their Basename integration. OnchainKit provides excellent patterns for:

### Identity Components
```typescript
import { Avatar, Name } from '@coinbase/onchainkit/identity';
import { base } from 'viem/chains';

// Display ENS avatar and name
<Avatar address={address} chain={base} />
<Name address={address} chain={base} />
```

### React Hooks
```typescript
import { useAvatar, useName } from '@coinbase/onchainkit/identity';

const { data: avatar, isLoading: avatarIsLoading } = useAvatar({ 
  ensName: 'zizzamia.base.eth', 
  chain: base 
});
```

### Utility Functions
```typescript
import { getAvatar, getName } from '@coinbase/onchainkit/identity';

const avatar = await getAvatar({ ensName: 'zizzamia.base.eth', chain: base });
const name = await getName({ address, chain: base });
```

## Implementation Guide

### 1. Frontend Setup

Install required dependencies:
```bash
pnpm add @ensdomains/ensjs viem wagmi @rainbow-me/rainbowkit
```

### 2. Wallet Integration

Our platform uses RainbowKit for wallet connections:
```typescript
import { ConnectButton } from '@rainbow-me/rainbowkit';
import { useAccount, useChainId } from 'wagmi';

const { address, isConnected } = useAccount();
const chainId = useChainId();
```

### 3. ENS Utilities

We provide comprehensive ENS utilities in `lib/ens-utils.ts`:
- `getENSOwner()`: Get the owner of an ENS name
- `isValidENSName()`: Validate ENS name format
- `transferENSOwnership()`: Transfer ENS ownership
- `getENSAvatar()`: Get ENS avatar/profile image

### 4. API Integration

Use our API service in `lib/api.ts` for backend communication:
```typescript
import { apiService } from '@/lib/api';

// Register ENS root
const response = await apiService.registerENSRoot(data, token);

// Check availability
const availability = await apiService.checkSubnameAvailability(subName, chain, token);

// Claim subname
const claimResult = await apiService.claimSubname(data, token);
```

## Error Handling

The API provides detailed error responses with specific error codes:

### Common Error Scenarios
- **400**: Validation errors (invalid ENS name, missing parameters)
- **401**: Authentication errors (invalid token, expired session)
- **404**: ENS root not found for application
- **409**: Conflict (ENS root already registered, subname already claimed)
- **500**: Internal server errors (blockchain interaction failures)

### Error Response Format
```json
{
  "success": false,
  "message": "Error message describing what went wrong",
  "error": "Detailed error (only in development)",
  "details": {
    "functionName": "claimName",
    "args": ["user123"],
    "account": "******************************************",
    "contractAddress": "0x123...def",
    "chainId": ********,
    "estimatedGas": "21000",
    "gasPrice": "**********",
    "estimatedCost": "2**********000",
    "accountBalance": "**********000000",
    "required": "2**********000",
    "shortBy": "-**************"
  }
}
```

## Security Considerations

### 1. Wallet Authentication
- All ENS operations require wallet connection
- Smart contract interactions are signed by the user's wallet
- No private keys are stored on our servers

### 2. API Authentication
- Bearer token authentication for all API calls
- Tokens are validated against the developer's session
- Rate limiting to prevent abuse

### 3. Smart Contract Security
- Contracts are deployed on Sepolia testnet for development
- Production deployment will use audited contracts
- Gas estimation and balance checks before transactions

## Testing

### Development Environment
- **Network**: Sepolia Testnet
- **Test Account**: `<EMAIL>` / `123456`
- **Faucet**: Use Sepolia faucet for test ETH

### Testing Checklist
- [ ] Wallet connection with RainbowKit
- [ ] ENS root registration
- [ ] Subname availability checking
- [ ] Subname claiming (regular and gasless)
- [ ] Error handling for all scenarios
- [ ] UI responsiveness across devices

## Next Steps

1. **Production Deployment**: Deploy contracts to Ethereum 
2. **Gas Optimization**: Implement more efficient gas usage patterns
3. **Enhanced UI**: Add more interactive features and animations
4. **Analytics**: Track ENS usage and adoption metrics
5. **Integration**: Connect with other Web3 services and protocols

## Resources

- [ENS Documentation](https://docs.ens.domains/)
- [OnchainKit Documentation](https://docs.base.org/onchainkit/)
- [RainbowKit Documentation](https://rainbowkit.com/)
- [Viem Documentation](https://viem.sh/)
- [Wagmi Documentation](https://wagmi.sh/)

---

For technical support or questions about the ENS integration, please contact the development team or refer to the API documentation.
