# 🚀 Crefy Connect

**Web3 Identity Infrastructure for Developers**

Crefy Connect is a comprehensive Web3 identity platform that provides developers with the tools to build secure, decentralized applications with seamless wallet integration, credential management, and identity verification.

![Crefy Connect Banner](https://via.placeholder.com/1200x400/4B0082/FFFFFF?text=Crefy+Connect+-+Web3+Identity+Infrastructure)

## ✨ Features

### 🔐 **Secure Authentication**
- **Developer Signup**: Self-service registration for developers
- **Admin Access**: Secure admin panel with environment-based credentials
- **Role-based Access Control**: Separate dashboards for developers and administrators

### 🌐 **Web3 Integration**
- **Coinbase Wallet Integration**: Seamless connection with Coinbase Wallet for secure Web3 interactions
- **Credential Management**: Issue, verify, and manage digital credentials
- **Zero-Knowledge Proofs**: Privacy-preserving identity verification
- **API Key Management**: Secure API access for your applications

### 🛠️ **Developer Tools**
- **Modular Architecture**: Plug-and-play components for any Web3 use case
- **RESTful APIs**: Easy integration with existing applications
- **Webhook Support**: Real-time notifications for credential events
- **Comprehensive Dashboard**: Monitor usage, manage projects, and track analytics

### 👨‍💼 **Admin Features**
- **User Management**: Oversee developer accounts and permissions
- **Credential Schema Management**: Define and manage credential types
- **System Reports**: Analytics and usage insights
- **Platform Configuration**: System-wide settings and controls

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend API   │    │   Blockchain    │
│   (Next.js)     │◄──►│   (Node.js)     │◄──►│   Integration   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌────────────���────┐
│   Auth System   │    │   Database      │    │   IPFS Storage  │
│   (JWT/OAuth)   │    │   (PostgreSQL)  │    │   (Metadata)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 Quick Start

### Prerequisites

- **Node.js** 18+ 
- **npm** or **pnpm**
- **Git**

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-org/crefy-connect-frontend.git
   cd crefy-connect-frontend
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   pnpm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   ```
   
   Edit `.env.local` with your configuration:
   ```env
   # Admin credentials
   ADMIN_EMAIL=<EMAIL>
   ADMIN_PASSWORD=your-secure-password
   
   # Database (if applicable)
   DATABASE_URL=your-database-url
   
   # API Keys (if applicable)
   NEXT_PUBLIC_API_URL=http://localhost:3000/api
   ```

4. **Run the development server**
   ```bash
   npm run dev
   # or
   pnpm dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## ���� Usage

### For Developers

1. **Sign Up**: Create a developer account at `/auth/signup`
2. **Dashboard Access**: Access your dashboard at `/dashboard/dashboard`
3. **API Keys**: Generate and manage API keys for your applications
4. **Wallet Integration**: Connect and manage multiple Web3 wallets
5. **Credentials**: Issue and verify digital credentials
6. **Webhooks**: Set up real-time notifications

### For Administrators

1. **Sign In**: Use admin credentials at `/auth/signin` (select Admin role)
2. **Admin Panel**: Access admin dashboard at `/dashboard/admin`
3. **User Management**: Oversee developer accounts
4. **Schema Management**: Define credential schemas
5. **Reports**: View system analytics and usage reports

## 🛠️ Tech Stack

### Frontend
- **Framework**: Next.js 15.3.3 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS 4.0
- **UI Components**: Radix UI primitives
- **Animations**: Framer Motion
- **Icons**: Lucide React

### Backend Integration
- **API Routes**: Next.js API routes
- **Authentication**: Custom auth context with localStorage
- **Form Handling**: React Hook Form with Zod validation
- **Charts**: Recharts for analytics

### Development Tools
- **Build Tool**: Turbopack (Next.js)
- **Linting**: ESLint
- **Type Checking**: TypeScript 5+
- **Package Manager**: npm/pnpm

## 📁 Project Structure

```
crefy-connect-frontend/
├── app/                          # Next.js App Router
│   ├── api/                      # API routes
│   │   └── auth/                 # Authentication endpoints
│   ├── auth/                     # Authentication pages
│   │   ├── signin/               # Sign in page
│   │   └── signup/               # Sign up page
│   ├── dashboard/                # Dashboard layouts and pages
│   │   ├── admin/                # Admin dashboard
│   │   │   ├── users/            # User management
│   │   │   ├── reports/          # System reports
│   │   │   └── credential-schemas/ # Schema management
│   │   └── dashboard/            # Developer dashboard
│   │       ├── api-keys/         # API key management
│   │       ├── credentials/      # Credential management
│   │       ├── wallets/          # Wallet integration
│   │       ├── webhooks/         # Webhook configuration
│   │       └── settings/         # User settings
│   ├── landing/                  # Landing page
│   └── globals.css               # Global styles
├── components/                   # Reusable components
│   ├── ui/                       # Base UI components
│   └── shared/                   # Shared components
│       ├── auth/                 # Authentication components
│       ├── dashboard/            # Dashboard components
│       └── ...                   # Other shared components
├── lib/                          # Utility libraries
│   └── auth-context.tsx          # Authentication context
├── hooks/                        # Custom React hooks
├── styles/                       # Additional stylesheets
└── public/                       # Static assets
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `ADMIN_EMAIL` | Admin login email | Yes |
| `ADMIN_PASSWORD` | Admin login password | Yes |
| `DATABASE_URL` | Database connection string | No |
| `NEXT_PUBLIC_API_URL` | Public API endpoint | No |

### Authentication Setup

The platform uses a dual authentication system:

- **Developers**: Can sign up through the UI
- **Admins**: Use credentials from environment variables

See [ADMIN_SETUP.md](./ADMIN_SETUP.md) for detailed admin configuration.

## 🎨 Customization

### Theming

The platform uses a custom color scheme based on:
- **Primary**: `#4B0082` (Indigo)
- **Secondary**: `#B497D6` (Light Purple)
- **Accent**: Various gradients and opacity variations

### Styling

- **Tailwind CSS**: Utility-first CSS framework
- **Custom Animations**: CSS-in-JS animations for enhanced UX
- **Responsive Design**: Mobile-first approach
- **Dark Mode**: Built-in theme switching (if enabled)

## 🧪 Testing

```bash
# Run tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

## 🚀 Deployment

### Vercel (Recommended)

1. **Connect your repository** to Vercel
2. **Set environment variables** in Vercel dashboard
3. **Deploy** automatically on push to main branch

### Docker

```bash
# Build Docker image
docker build -t crefy-connect .

# Run container
docker run -p 3000:3000 crefy-connect
```

### Manual Deployment

```bash
# Build for production
npm run build

# Start production server
npm start
```

## 📚 API Documentation

### Authentication Endpoints

- `POST /api/auth/admin-check` - Validate admin credentials
- `POST /api/auth/developer-signup` - Developer registration
- `POST /api/auth/developer-signin` - Developer authentication

### Developer APIs

- `GET /api/developer/profile` - Get developer profile
- `POST /api/developer/api-keys` - Generate API key
- `GET /api/developer/credentials` - List credentials
- `POST /api/developer/credentials` - Issue credential

### Admin APIs

- `GET /api/admin/users` - List all users
- `POST /api/admin/users/:id/suspend` - Suspend user
- `GET /api/admin/reports` - System reports
- `POST /api/admin/schemas` - Create credential schema

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Workflow

1. **Fork** the repository
2. **Create** a feature branch (`git checkout -b feature/amazing-feature`)
3. **Commit** your changes (`git commit -m 'Add amazing feature'`)
4. **Push** to the branch (`git push origin feature/amazing-feature`)
5. **Open** a Pull Request

### Code Standards

- **TypeScript**: Strict mode enabled
- **ESLint**: Follow the configured rules
- **Prettier**: Code formatting
- **Conventional Commits**: Use conventional commit messages

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

### Documentation
- [Developer Docs](https://docs.crefyconnect.com)
- [API Reference](https://api.crefyconnect.com/docs)
- [Tutorials](https://tutorials.crefyconnect.com)

### Community
- [Discord](https://discord.gg/crefyconnect)
- [GitHub Discussions](https://github.com/your-org/crefy-connect/discussions)
- [Twitter](https://twitter.com/crefyconnect)

### Issues
- [Bug Reports](https://github.com/your-org/crefy-connect-frontend/issues/new?template=bug_report.md)
- [Feature Requests](https://github.com/your-org/crefy-connect-frontend/issues/new?template=feature_request.md)

## 🗺️ Roadmap

### Q1 2025
- [ ] Enhanced wallet integrations
- [ ] Advanced credential schemas
- [ ] Mobile app support
- [ ] Multi-language support

### Q2 2025
- [ ] Enterprise features
- [ ] Advanced analytics
- [ ] Third-party integrations
- [ ] Performance optimizations

### Q3 2025
- [ ] Decentralized governance
- [ ] Cross-chain support
- [ ] Advanced security features
- [ ] Developer marketplace

## 🙏 Acknowledgments

- **Next.js Team** for the amazing framework
- **Radix UI** for accessible component primitives
- **Tailwind CSS** for the utility-first CSS framework
- **Vercel** for hosting and deployment platform
- **Open Source Community** for inspiration and contributions

---

<div align="center">

**Built with ❤️ by the Crefy Connect Team**

[Website](https://crefyconnect.com) • [Documentation](https://docs.crefyconnect.com) • [Community](https://discord.gg/crefyconnect)

</div>